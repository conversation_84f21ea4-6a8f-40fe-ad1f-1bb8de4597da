import { Request, Response, NextFunction } from 'express';
import { AppError, ApiResponse } from '../types';

/**
 * 全局错误处理中间件
 * 统一处理应用中的所有错误
 */
export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  console.error('错误详情:', error);

  // 如果是 AppError，使用其状态码和消息
  if (error instanceof AppError) {
    const response: ApiResponse = {
      success: false,
      error: error.message
    };
    
    res.status(error.statusCode).json(response);
    return;
  }

  // 处理验证错误（Joi 错误通常包含 'validation' 关键字）
  if (error.message.includes('验证失败') || error.message.includes('不能为空') || error.message.includes('必须是')) {
    const response: ApiResponse = {
      success: false,
      error: error.message
    };
    
    res.status(400).json(response);
    return;
  }

  // 处理连接测试失败错误
  if (error.message.includes('连接失败') || error.message.includes('Connection failed')) {
    const response: ApiResponse = {
      success: false,
      error: '连接测试失败'
    };

    res.status(400).json(response);
    return;
  }

  // 默认的服务器错误
  const response: ApiResponse = {
    success: false,
    error: '服务器内部错误'
  };
  
  res.status(500).json(response);
}
