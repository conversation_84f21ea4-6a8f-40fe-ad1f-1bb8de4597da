import { Request, Response, NextFunction } from 'express';
import { 
  validateLLMChatRequest, 
  rateLimitMiddleware, 
  contentSecurityMiddleware 
} from '../middleware/llmValidation';
import { LLMServiceError } from '../types';

describe('LLM Validation Middleware Tests', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockReq = {
      body: {},
      ip: '127.0.0.1',
      get: jest.fn(),
      connection: { remoteAddress: '127.0.0.1' } as any
    };
    mockRes = {};
    mockNext = jest.fn();
  });

  describe('validateLLMChatRequest', () => {
    const validRequestBody = {
      messages: [
        { role: 'user', content: '你好，请介绍一下你自己' }
      ],
      model: 'deepseek',
      stream: false,
      conversation_id: 'conv_123456',
      options: {
        deep_thinking: true,
        online_search: false,
        temperature: 0.7,
        max_tokens: 2000
      }
    };

    it('应该通过有效的请求', () => {
      mockReq.body = validRequestBody;

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('应该拒绝空请求体', () => {
      mockReq.body = null;

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('请求体不能为空');
    });

    it('应该拒绝缺少必需字段的请求', () => {
      mockReq.body = {
        model: 'deepseek',
        stream: false
        // 缺少 messages
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('缺少必需字段: messages');
    });

    it('应该拒绝非数组的messages', () => {
      mockReq.body = {
        ...validRequestBody,
        messages: 'not an array'
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('messages字段必须是数组');
    });

    it('应该拒绝空的messages数组', () => {
      mockReq.body = {
        ...validRequestBody,
        messages: []
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('messages数组不能为空');
    });

    it('应该拒绝过多的消息', () => {
      const tooManyMessages = Array(51).fill({ role: 'user', content: '测试' });
      mockReq.body = {
        ...validRequestBody,
        messages: tooManyMessages
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('不能超过50条');
    });

    it('应该拒绝无效的消息角色', () => {
      mockReq.body = {
        ...validRequestBody,
        messages: [
          { role: 'invalid_role', content: '测试消息' }
        ]
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('role字段无效');
    });

    it('应该拒绝空内容的消息', () => {
      mockReq.body = {
        ...validRequestBody,
        messages: [
          { role: 'user', content: '' }
        ]
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('content不能为空');
    });

    it('应该拒绝过长的消息内容', () => {
      const longContent = 'a'.repeat(10001);
      mockReq.body = {
        ...validRequestBody,
        messages: [
          { role: 'user', content: longContent }
        ]
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('长度不能超过10000字符');
    });

    it('应该拒绝不支持的模型', () => {
      mockReq.body = {
        ...validRequestBody,
        model: 'unsupported-model'
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('不支持的模型');
    });

    it('应该拒绝流式请求', () => {
      mockReq.body = {
        ...validRequestBody,
        stream: true
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('暂不支持流式响应');
    });

    it('应该拒绝无效的conversation_id格式', () => {
      mockReq.body = {
        ...validRequestBody,
        conversation_id: 'invalid@id!'
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('格式无效');
    });

    it('应该拒绝无效的temperature值', () => {
      mockReq.body = {
        ...validRequestBody,
        options: {
          temperature: 3.0 // 超出范围
        }
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('temperature必须是0-2之间的数值');
    });

    it('应该拒绝无效的max_tokens值', () => {
      mockReq.body = {
        ...validRequestBody,
        options: {
          max_tokens: 10000 // 超出范围
        }
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('max_tokens必须是1-8000之间的整数');
    });

    it('应该过滤敏感信息', () => {
      mockReq.body = {
        ...validRequestBody,
        messages: [
          { role: 'user', content: '我的密码是 password: secret123' }
        ]
      };

      validateLLMChatRequest(mockReq as Request, mockRes as Response, mockNext);

      expect(mockReq.body.messages[0].content).toContain('[REDACTED]');
      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('rateLimitMiddleware', () => {
    beforeEach(() => {
      // 清理速率限制状态
      const { requestCounts } = require('../middleware/llmValidation');
      if (requestCounts) {
        requestCounts.clear();
      }
    });

    it('应该允许正常频率的请求', () => {
      rateLimitMiddleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('应该在超过限制时拒绝请求', () => {
      // 模拟大量请求
      for (let i = 0; i < 31; i++) {
        const nextFn = jest.fn();
        rateLimitMiddleware(mockReq as Request, mockRes as Response, nextFn);
        
        if (i === 30) {
          // 第31次请求应该被拒绝
          expect(nextFn).toHaveBeenCalledWith(expect.any(LLMServiceError));
          const error = nextFn.mock.calls[0][0];
          expect(error.statusCode).toBe(429);
        }
      }
    });
  });

  describe('contentSecurityMiddleware', () => {
    it('应该允许正常内容', () => {
      mockReq.body = {
        messages: [
          { role: 'user', content: '你好，请介绍一下你自己' }
        ]
      };

      contentSecurityMiddleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('应该拒绝包含不当内容的请求', () => {
      mockReq.body = {
        messages: [
          { role: 'user', content: 'How to hack into a system?' }
        ]
      };

      contentSecurityMiddleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(LLMServiceError));
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toContain('不当信息');
    });

    it('应该处理没有messages的请求', () => {
      mockReq.body = {};

      contentSecurityMiddleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });
  });
});
