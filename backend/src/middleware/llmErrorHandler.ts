import { Request, Response, NextFunction } from 'express';
import { LLMServiceError, ApiResponse } from '../types';

/**
 * LLM专用错误处理中间件
 * 处理LLM服务相关的所有错误类型
 */
export function llmErrorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // 记录错误详情
  logError(error, req);

  // 根据错误类型返回相应的响应
  if (error instanceof LLMServiceError) {
    handleLLMServiceError(error, res);
  } else if (isNetworkError(error)) {
    handleNetworkError(error, res);
  } else if (isValidationError(error)) {
    handleValidationError(error, res);
  } else if (isAuthenticationError(error)) {
    handleAuthenticationError(error, res);
  } else if (isRateLimitError(error)) {
    handleRateLimitError(error, res);
  } else {
    handleGenericError(error, res);
  }
}

/**
 * 处理LLM服务错误
 */
function handleLLMServiceError(error: LLMServiceError, res: Response): void {
  const response: ApiResponse = {
    success: false,
    error: error.message,
    requestId: generateRequestId(),
    timestamp: Date.now()
  };

  res.status(error.statusCode).json(response);
}

/**
 * 处理网络错误
 */
function handleNetworkError(error: Error, res: Response): void {
  console.error('🌐 网络错误:', error.message);

  const response: ApiResponse = {
    success: false,
    error: 'LLM服务暂时不可用，请稍后重试',
    requestId: generateRequestId(),
    timestamp: Date.now()
  };

  res.status(503).json(response);
}

/**
 * 处理验证错误
 */
function handleValidationError(error: Error, res: Response): void {
  const response: ApiResponse = {
    success: false,
    error: error.message,
    requestId: generateRequestId(),
    timestamp: Date.now()
  };

  res.status(400).json(response);
}

/**
 * 处理认证错误
 */
function handleAuthenticationError(error: Error, res: Response): void {
  const response: ApiResponse = {
    success: false,
    error: '认证失败，请检查您的访问权限',
    requestId: generateRequestId(),
    timestamp: Date.now()
  };

  res.status(401).json(response);
}

/**
 * 处理速率限制错误
 */
function handleRateLimitError(error: Error, res: Response): void {
  const response: ApiResponse = {
    success: false,
    error: '请求过于频繁，请稍后再试',
    requestId: generateRequestId(),
    timestamp: Date.now()
  };

  res.status(429).json(response);
}

/**
 * 处理通用错误
 */
function handleGenericError(error: Error, res: Response): void {
  console.error('❌ 未知错误:', error);

  const response: ApiResponse = {
    success: false,
    error: '服务器内部错误，请稍后重试',
    requestId: generateRequestId(),
    timestamp: Date.now()
  };

  res.status(500).json(response);
}

/**
 * 记录错误日志
 */
function logError(error: Error, req: Request): void {
  const errorLog = {
    timestamp: new Date().toISOString(),
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack
    },
    request: {
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      body: sanitizeLogData(req.body)
    }
  };

  console.error('🚨 LLM错误日志:', JSON.stringify(errorLog, null, 2));

  // 在生产环境中，这里可以发送到日志服务
  if (process.env.NODE_ENV === 'production') {
    // 发送到外部日志服务（如ELK、Sentry等）
    sendToLogService(errorLog);
  }
}

/**
 * 清理日志数据中的敏感信息
 */
function sanitizeLogData(data: any): any {
  if (!data) return data;

  const sanitized = JSON.parse(JSON.stringify(data));

  // 移除或脱敏敏感字段
  const sensitiveFields = ['password', 'token', 'api_key', 'secret'];
  
  function sanitizeObject(obj: any): void {
    if (typeof obj !== 'object' || obj === null) return;

    for (const key in obj) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        obj[key] = '[REDACTED]';
      } else if (typeof obj[key] === 'object') {
        sanitizeObject(obj[key]);
      }
    }
  }

  sanitizeObject(sanitized);
  return sanitized;
}

/**
 * 发送到外部日志服务
 */
function sendToLogService(errorLog: any): void {
  // 这里实现发送到外部日志服务的逻辑
  // 例如：Sentry、ELK Stack、CloudWatch等
  console.log('📤 发送错误日志到外部服务...');
}

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 判断是否为网络错误
 */
function isNetworkError(error: Error): boolean {
  const networkErrorMessages = [
    'network',
    'timeout',
    'connection',
    'ECONNREFUSED',
    'ENOTFOUND',
    'ETIMEDOUT'
  ];

  return networkErrorMessages.some(msg => 
    error.message.toLowerCase().includes(msg.toLowerCase())
  );
}

/**
 * 判断是否为验证错误
 */
function isValidationError(error: Error): boolean {
  const validationErrorMessages = [
    '验证失败',
    '格式无效',
    '字段必须',
    '不能为空',
    '长度不能超过',
    'validation failed',
    'invalid format',
    'required field'
  ];

  return validationErrorMessages.some(msg => 
    error.message.toLowerCase().includes(msg.toLowerCase())
  );
}

/**
 * 判断是否为认证错误
 */
function isAuthenticationError(error: Error): boolean {
  const authErrorMessages = [
    'unauthorized',
    'authentication',
    'invalid token',
    'access denied',
    '认证失败',
    '无权限'
  ];

  return authErrorMessages.some(msg => 
    error.message.toLowerCase().includes(msg.toLowerCase())
  );
}

/**
 * 判断是否为速率限制错误
 */
function isRateLimitError(error: Error): boolean {
  return error.message.includes('rate limit') || 
         error.message.includes('请求过于频繁') ||
         (error as any).statusCode === 429;
}

/**
 * 错误统计中间件
 */
const errorStats = {
  total: 0,
  byType: new Map<string, number>(),
  byEndpoint: new Map<string, number>()
};

export function errorStatsMiddleware(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // 更新错误统计
  errorStats.total++;
  
  const errorType = error.constructor.name;
  errorStats.byType.set(errorType, (errorStats.byType.get(errorType) || 0) + 1);
  
  const endpoint = `${req.method} ${req.route?.path || req.path}`;
  errorStats.byEndpoint.set(endpoint, (errorStats.byEndpoint.get(endpoint) || 0) + 1);

  // 继续到下一个错误处理中间件
  next(error);
}

/**
 * 获取错误统计信息
 */
export function getErrorStats(): any {
  return {
    total: errorStats.total,
    byType: Object.fromEntries(errorStats.byType),
    byEndpoint: Object.fromEntries(errorStats.byEndpoint),
    timestamp: new Date().toISOString()
  };
}

/**
 * 重置错误统计
 */
export function resetErrorStats(): void {
  errorStats.total = 0;
  errorStats.byType.clear();
  errorStats.byEndpoint.clear();
}
