import { PrismaClient } from '@prisma/client';
import { Client as PgClient } from 'pg';
import mysql from 'mysql2/promise';
import sqlite3 from 'sqlite3';
import fs from 'fs/promises';
import csv from 'csv-parser';
import * as XLSX from 'xlsx';
import { Readable } from 'stream';

import {
  DataSourceType,
  ConnectionConfig,
  DatabaseConnectionConfig,
  FileConnectionConfig,
  SQLiteConnectionConfig,
  DataSourceStatus,
  DataSourceListItem,
  DataSourceSchema,
  AppError,
  DatabaseError
} from '../types';
import { encryptConnectionConfig, decryptConnectionConfig } from '../utils/encryption';
import { validateDataSourceConfig } from '../utils/validation';
import { MetadataService } from './metadataService';

export class DataSourceService {
  private metadataService: MetadataService;

  constructor(private prisma: PrismaClient) {
    this.metadataService = new MetadataService(prisma);
  }

  /**
   * 获取所有数据源列表
   */
  async getDataSources(): Promise<DataSourceListItem[]> {
    try {
      const dataSources = await this.prisma.dataSource.findMany({
        select: {
          id: true,
          name: true,
          type: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // 检查每个数据源的连接状态
      const dataSourcesWithStatus = await Promise.all(
        dataSources.map(async (ds) => {
          const status = await this.checkConnectionStatus(ds.id);
          return {
            ...ds,
            type: ds.type as DataSourceType,
            status,
            createdAt: ds.createdAt.toISOString(),
            updatedAt: ds.updatedAt.toISOString()
          };
        })
      );

      return dataSourcesWithStatus;
    } catch (error) {
      throw new DatabaseError('获取数据源列表失败');
    }
  }

  /**
   * 根据ID获取数据源详情
   */
  async getDataSourceById(id: string): Promise<DataSourceSchema> {
    try {
      const dataSource = await this.prisma.dataSource.findUnique({
        where: { id },
        include: {
          dataTables: {
            include: {
              dataColumns: true
            }
          }
        }
      });

      if (!dataSource) {
        throw new AppError('数据源不存在', 404);
      }

      // 获取关联关系
      const relationships = await this.prisma.dataRelationship.findMany({
        where: {
          OR: [
            { fromColumn: { dataTable: { dataSourceId: id } } },
            { toColumn: { dataTable: { dataSourceId: id } } }
          ]
        },
        select: {
          id: true,
          fromColumnId: true,
          toColumnId: true,
          relationshipType: true,
          isManual: true,
          discoveryMethod: true,
          confidence: true,
          confidenceScore: true,
          evidence: true,
          metadata: true
        }
      });

      return {
        id: dataSource.id,
        name: dataSource.name,
        type: dataSource.type as DataSourceType,
        tables: dataSource.dataTables.map(table => ({
          id: table.id,
          originalName: table.originalTableName,
          aliasName: table.aliasName || undefined,
          description: table.description || undefined,
          columns: table.dataColumns.map(column => ({
            id: column.id,
            originalName: column.originalColumnName,
            aliasName: column.aliasName || undefined,
            dataType: column.originalDataType,
            description: column.description || undefined,
            isPrimaryKey: column.isPrimaryKey
          }))
        })),
        relationships: relationships.map(rel => ({
          id: rel.id,
          fromColumnId: rel.fromColumnId,
          toColumnId: rel.toColumnId,
          type: rel.relationshipType as any,
          isManual: rel.isManual,
          discoveryMethod: rel.discoveryMethod as any,
          confidence: rel.confidence as any,
          confidenceScore: rel.confidenceScore ?? undefined,
          evidence: rel.evidence ? JSON.parse(rel.evidence) : [],
          metadata: rel.metadata ? JSON.parse(rel.metadata) : undefined
        }))
      };
    } catch (error) {
      if (error instanceof AppError) throw error;
      console.error('获取数据源详情失败，详细错误:', error);
      throw new DatabaseError(`获取数据源详情失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 创建新数据源
   */
  async createDataSource(name: string, type: DataSourceType, connectionConfig: ConnectionConfig): Promise<string> {
    try {
      // 验证连接配置
      validateDataSourceConfig(type, connectionConfig);

      // 测试连接
      await this.testConnection(type, connectionConfig);

      // 加密连接配置
      const encryptedConfig = encryptConnectionConfig(connectionConfig);

      // 创建数据源记录
      const dataSource = await this.prisma.dataSource.create({
        data: {
          name,
          type,
          connectionConfig: encryptedConfig
        }
      });

      // 同步元数据
      await this.syncMetadata(dataSource.id);

      return dataSource.id;
    } catch (error) {
      if (error instanceof AppError) throw error;
      console.error('创建数据源详细错误:', error);
      throw new DatabaseError(`创建数据源失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 更新数据源
   */
  async updateDataSource(id: string, name?: string, connectionConfig?: ConnectionConfig): Promise<void> {
    try {
      const dataSource = await this.prisma.dataSource.findUnique({
        where: { id }
      });

      if (!dataSource) {
        throw new AppError('数据源不存在', 404);
      }

      const updateData: any = {};

      if (name) {
        updateData.name = name;
      }

      if (connectionConfig) {
        // 验证连接配置
        validateDataSourceConfig(dataSource.type as DataSourceType, connectionConfig);
        
        // 测试连接
        await this.testConnection(dataSource.type as DataSourceType, connectionConfig);
        
        // 加密连接配置
        updateData.connectionConfig = encryptConnectionConfig(connectionConfig);
      }

      await this.prisma.dataSource.update({
        where: { id },
        data: updateData
      });

      // 如果更新了连接配置，重新同步元数据
      if (connectionConfig) {
        await this.syncMetadata(id);
      }
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new DatabaseError('更新数据源失败');
    }
  }

  /**
   * 删除数据源
   */
  async deleteDataSource(id: string): Promise<void> {
    try {
      const dataSource = await this.prisma.dataSource.findUnique({
        where: { id }
      });

      if (!dataSource) {
        throw new AppError('数据源不存在', 404);
      }

      await this.prisma.dataSource.delete({
        where: { id }
      });
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new DatabaseError('删除数据源失败');
    }
  }

  /**
   * 测试数据源连接
   */
  async testConnection(type: DataSourceType, config: ConnectionConfig): Promise<boolean> {
    try {
      switch (type) {
        case 'postgresql':
          return await this.testPostgreSQLConnection(config as DatabaseConnectionConfig);
        case 'mysql':
          return await this.testMySQLConnection(config as DatabaseConnectionConfig);
        case 'sqlite':
          return await this.testSQLiteConnection(config as SQLiteConnectionConfig);
        case 'csv':
        case 'excel':
          return await this.testFileConnection(config as FileConnectionConfig);
        default:
          throw new AppError('不支持的数据源类型', 400);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new AppError(`连接测试失败: ${errorMessage}`, 400);
    }
  }

  /**
   * 检查数据源连接状态
   */
  async checkConnectionStatus(id: string): Promise<DataSourceStatus> {
    try {
      const dataSource = await this.prisma.dataSource.findUnique({
        where: { id }
      });

      if (!dataSource) {
        return 'error';
      }

      const config = decryptConnectionConfig(dataSource.connectionConfig);
      const isConnected = await this.testConnection(dataSource.type as DataSourceType, config);
      
      return isConnected ? 'connected' : 'disconnected';
    } catch (error) {
      return 'error';
    }
  }

  /**
   * 同步数据源元数据
   */
  async syncMetadata(dataSourceId: string): Promise<void> {
    await this.metadataService.syncMetadata(dataSourceId);
  }

  // 私有方法
  private async testPostgreSQLConnection(config: DatabaseConnectionConfig): Promise<boolean> {
    const client = new PgClient({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl
    });

    try {
      await client.connect();
      await client.query('SELECT 1');
      return true;
    } finally {
      await client.end();
    }
  }

  private async testMySQLConnection(config: DatabaseConnectionConfig): Promise<boolean> {
    const connection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl ? {} : undefined
    });

    try {
      await connection.execute('SELECT 1');
      return true;
    } finally {
      await connection.end();
    }
  }

  /**
   * 测试 SQLite 数据库连接
   */
  private async testSQLiteConnection(config: SQLiteConnectionConfig): Promise<boolean> {
    return new Promise((resolve, reject) => {
      // 确定 SQLite 打开模式
      let mode = sqlite3.OPEN_READWRITE;
      if (config.mode === 'readonly') {
        mode = sqlite3.OPEN_READONLY;
      } else if (config.mode === 'create') {
        mode = sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE;
      }

      const db = new sqlite3.Database(config.filePath, mode, (err) => {
        if (err) {
          reject(new Error(`SQLite 连接失败: ${err.message}`));
          return;
        }

        // 执行简单查询测试连接
        db.get('SELECT 1 as test', (err, row) => {
          db.close((closeErr) => {
            if (closeErr) {
              console.warn('SQLite 数据库关闭时出现警告:', closeErr.message);
            }
          });

          if (err) {
            reject(new Error(`SQLite 查询测试失败: ${err.message}`));
          } else {
            resolve(true);
          }
        });
      });
    });
  }

  private async testFileConnection(config: FileConnectionConfig): Promise<boolean> {
    try {
      const stats = await fs.stat(config.path);
      return stats.isFile();
    } catch (error) {
      return false;
    }
  }
}
