import { Request, Response, NextFunction } from 'express';
import { SemanticService } from '../services/semanticService';
import { prisma } from '../lib/prisma';
import {
  validate,
  tableAliasUpdateSchema,
  columnAliasUpdateSchema,
  relationshipCreateSchema
} from '../utils/validation';
import { ApiResponse, AppError } from '../types';

const semanticService = new SemanticService(prisma);

/**
 * 更新数据表别名和描述
 */
export async function updateTableAlias(req: Request, res: Response, next: NextFunction) {
  try {
    const { tableId } = req.params;
    
    if (!tableId) {
      throw new AppError('表ID不能为空', 400);
    }
    
    const validatedData = validate(tableAliasUpdateSchema, req.body);
    const { aliasName, description } = validatedData;
    
    await semanticService.updateTableAlias(tableId, aliasName, description);
    
    const response: ApiResponse = {
      success: true,
      message: '表别名更新成功'
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 更新数据列别名和描述
 */
export async function updateColumnAlias(req: Request, res: Response, next: NextFunction) {
  try {
    const { columnId } = req.params;
    
    if (!columnId) {
      throw new AppError('列ID不能为空', 400);
    }
    
    const validatedData = validate(columnAliasUpdateSchema, req.body);
    const { aliasName, description } = validatedData;
    
    await semanticService.updateColumnAlias(columnId, aliasName, description);
    
    const response: ApiResponse = {
      success: true,
      message: '列别名更新成功'
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 创建数据关联关系
 */
export async function createRelationship(req: Request, res: Response, next: NextFunction) {
  try {
    const validatedData = validate(relationshipCreateSchema, req.body);
    const { fromColumnId, toColumnId, relationshipType } = validatedData;
    
    const relationshipId = await semanticService.createRelationship(
      fromColumnId, 
      toColumnId, 
      relationshipType
    );
    
    const response: ApiResponse = {
      success: true,
      data: { id: relationshipId },
      message: '关联关系创建成功'
    };
    
    res.status(201).json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 更新关联关系
 */
export async function updateRelationship(req: Request, res: Response, next: NextFunction) {
  try {
    const { relationshipId } = req.params;
    const { relationshipType } = req.body;
    
    if (!relationshipId) {
      throw new AppError('关联关系ID不能为空', 400);
    }
    
    if (!relationshipType) {
      throw new AppError('关联关系类型不能为空', 400);
    }
    
    await semanticService.updateRelationship(relationshipId, relationshipType);
    
    const response: ApiResponse = {
      success: true,
      message: '关联关系更新成功'
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 删除关联关系
 */
export async function deleteRelationship(req: Request, res: Response, next: NextFunction) {
  try {
    const { relationshipId } = req.params;
    
    if (!relationshipId) {
      throw new AppError('关联关系ID不能为空', 400);
    }
    
    await semanticService.deleteRelationship(relationshipId);
    
    const response: ApiResponse = {
      success: true,
      message: '关联关系删除成功'
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 获取语义层上下文
 */
export async function getSemanticContext(req: Request, res: Response, next: NextFunction) {
  try {
    const context = await semanticService.getSemanticContext();
    
    const response: ApiResponse = {
      success: true,
      data: { context }
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 自动发现关联关系
 */
export async function discoverRelationships(req: Request, res: Response, next: NextFunction) {
  try {
    const { dataSourceId } = req.params;
    const {
      enableForeignKeyDetection = true,
      enableNamingConvention = true,
      enableDataAnalysis = true,
      enableCrossDataSource = false,
      confidenceThreshold = 0.3,
      maxSuggestions = 50,
      autoApply = false,
      autoApplyThreshold = 0.8
    } = req.body;

    if (!dataSourceId) {
      throw new AppError('数据源ID不能为空', 400);
    }

    // 导入关联关系发现服务
    const { RelationshipDiscoveryService } = await import('../services/relationshipDiscoveryService');
    const { PrismaClient } = await import('@prisma/client');

    const prisma = new PrismaClient();
    const discoveryService = new RelationshipDiscoveryService(prisma);

    // 发现关联关系
    const discoveredRelationships = await discoveryService.discoverRelationships(dataSourceId, {
      enableForeignKeyDetection,
      enableNamingConvention,
      enableDataAnalysis,
      enableCrossDataSource,
      namingPatterns: [],
      confidenceThreshold,
      maxSuggestions
    });

    let appliedCount = 0;
    let applyResults = null;

    // 如果启用自动应用
    if (autoApply && discoveredRelationships.length > 0) {
      applyResults = await discoveryService.applyDiscoveredRelationships(
        discoveredRelationships,
        autoApplyThreshold
      );
      appliedCount = applyResults.applied;
    }

    const response: ApiResponse = {
      success: true,
      data: {
        discoveredCount: discoveredRelationships.length,
        appliedCount,
        relationships: discoveredRelationships,
        applyResults
      },
      message: `成功发现 ${discoveredRelationships.length} 个关联关系${appliedCount > 0 ? `，自动应用 ${appliedCount} 个` : ''}`
    };

    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 发现跨数据源关联关系
 */
export async function discoverCrossSourceRelationships(req: Request, res: Response, next: NextFunction) {
  try {
    const { sourceDataSourceId } = req.params;
    const {
      targetDataSourceId,
      confidenceThreshold = 0.3,
      maxSuggestions = 50,
      autoApply = false,
      autoApplyThreshold = 0.8
    } = req.body;

    if (!sourceDataSourceId) {
      throw new AppError('源数据源ID不能为空', 400);
    }

    const { RelationshipDiscoveryService } = await import('../services/relationshipDiscoveryService');
    const { PrismaClient } = await import('@prisma/client');

    const prisma = new PrismaClient();
    const discoveryService = new RelationshipDiscoveryService(prisma);

    // 发现跨数据源关联关系
    const discoveredRelationships = await discoveryService.discoverCrossSourceRelationships(
      sourceDataSourceId,
      targetDataSourceId,
      {
        enableForeignKeyDetection: false, // 跨数据源不支持外键检测
        enableNamingConvention: true,
        enableDataAnalysis: true,
        enableCrossDataSource: true,
        namingPatterns: [],
        confidenceThreshold,
        maxSuggestions
      }
    );

    let appliedCount = 0;
    let applyResults = null;

    if (autoApply && discoveredRelationships.length > 0) {
      applyResults = await discoveryService.applyDiscoveredRelationships(
        discoveredRelationships,
        autoApplyThreshold
      );
      appliedCount = applyResults.applied;
    }

    const response: ApiResponse = {
      success: true,
      data: {
        discoveredCount: discoveredRelationships.length,
        appliedCount,
        relationships: discoveredRelationships,
        applyResults
      },
      message: `成功发现 ${discoveredRelationships.length} 个跨数据源关联关系${appliedCount > 0 ? `，自动应用 ${appliedCount} 个` : ''}`
    };

    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 批量发现所有关联关系
 */
export async function discoverAllRelationships(req: Request, res: Response, next: NextFunction) {
  try {
    const {
      enableForeignKeyDetection = true,
      enableNamingConvention = true,
      enableDataAnalysis = true,
      enableCrossDataSource = true,
      confidenceThreshold = 0.3,
      maxSuggestions = 100,
      autoApply = false,
      autoApplyThreshold = 0.8
    } = req.body;

    const { RelationshipDiscoveryService } = await import('../services/relationshipDiscoveryService');
    const { PrismaClient } = await import('@prisma/client');

    const prisma = new PrismaClient();
    const discoveryService = new RelationshipDiscoveryService(prisma);

    // 批量发现所有关联关系
    const discoveredRelationships = await discoveryService.discoverAllRelationships({
      enableForeignKeyDetection,
      enableNamingConvention,
      enableDataAnalysis,
      enableCrossDataSource,
      namingPatterns: [],
      confidenceThreshold,
      maxSuggestions
    });

    let appliedCount = 0;
    let applyResults = null;

    if (autoApply && discoveredRelationships.length > 0) {
      applyResults = await discoveryService.applyDiscoveredRelationships(
        discoveredRelationships,
        autoApplyThreshold
      );
      appliedCount = applyResults.applied;
    }

    // 计算统计信息
    const { ConfidenceScorer } = await import('../utils/confidenceScorer');
    const statistics = ConfidenceScorer.getConfidenceStatistics(discoveredRelationships);

    const response: ApiResponse = {
      success: true,
      data: {
        discoveredCount: discoveredRelationships.length,
        appliedCount,
        relationships: discoveredRelationships,
        statistics,
        applyResults
      },
      message: `成功发现 ${discoveredRelationships.length} 个关联关系${appliedCount > 0 ? `，自动应用 ${appliedCount} 个` : ''}`
    };

    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 应用发现的关联关系
 */
export async function applyDiscoveredRelationships(req: Request, res: Response, next: NextFunction) {
  try {
    const { relationships, autoApplyThreshold = 0.8 } = req.body;

    if (!relationships || !Array.isArray(relationships)) {
      throw new AppError('关联关系列表不能为空', 400);
    }

    const { RelationshipDiscoveryService } = await import('../services/relationshipDiscoveryService');
    const { PrismaClient } = await import('@prisma/client');

    const prisma = new PrismaClient();
    const discoveryService = new RelationshipDiscoveryService(prisma);

    const applyResults = await discoveryService.applyDiscoveredRelationships(
      relationships,
      autoApplyThreshold
    );

    const response: ApiResponse = {
      success: true,
      data: applyResults,
      message: `成功应用 ${applyResults.applied} 个关联关系，跳过 ${applyResults.skipped} 个，失败 ${applyResults.failed} 个`
    };

    res.json(response);
  } catch (error) {
    next(error);
  }
}
