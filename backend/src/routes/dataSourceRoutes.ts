import { Router } from 'express';
import {
  getDataSources,
  getDataSourceById,
  getDataSourceSchema,
  createDataSource,
  updateDataSource,
  deleteDataSource,
  testConnection,
  syncMetadata,
  checkConnectionStatus
} from '../controllers/dataSourceController';
import { authenticateJWT, optionalAuthenticateJWT } from '../middleware/auth';

const router = Router();

// 获取所有数据源 - 需要认证
router.get('/', authenticateJWT, getDataSources);

// 测试连接（不需要先创建数据源）- 需要认证
router.post('/test-connection', authenticateJWT, testConnection);

// 根据ID获取数据源详情 - 需要认证
router.get('/:id', authenticateJWT, getDataSourceById);

// 获取数据源的Schema信息 - 需要认证
router.get('/:id/schema', authenticateJWT, getDataSourceSchema);

// 检查数据源连接状态 - 需要认证
router.get('/:id/status', authenticateJWT, checkConnectionStatus);

// 同步数据源元数据 - 需要认证
router.post('/:id/sync', authenticateJWT, syncMetadata);

// 创建新数据源 - 需要认证
router.post('/', authenticateJWT, createDataSource);

// 更新数据源 - 需要认证
router.put('/:id', authenticateJWT, updateDataSource);

// 删除数据源 - 需要认证
router.delete('/:id', authenticateJWT, deleteDataSource);

export default router;
