-- CreateTable
CREATE TABLE "llm_conversations" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT,
    "user_id" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "llm_messages" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "conversation_id" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "model" TEXT,
    "parent_id" TEXT,
    "request_id" TEXT,
    "prompt_tokens" INTEGER,
    "completion_tokens" INTEGER,
    "total_tokens" INTEGER,
    "finish_reason" TEXT,
    "deep_thinking" BOOLEAN NOT NULL DEFAULT false,
    "online_search" BOOLEAN NOT NULL DEFAULT false,
    "metadata" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "llm_messages_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "llm_conversations" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "llm_messages_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "llm_messages" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);
