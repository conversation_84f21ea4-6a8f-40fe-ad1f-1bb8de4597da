import { RedisClientWrapper } from '../../utils/redisClient';

// 模拟Redis客户端
const mockRedisClient = {
  connect: jest.fn(),
  quit: jest.fn(),
  get: jest.fn(),
  set: jest.fn(),
  setEx: jest.fn(),
  del: jest.fn(),
  exists: jest.fn(),
  expire: jest.fn(),
  ping: jest.fn(),
  on: jest.fn()
};

// 模拟redis模块
jest.mock('redis', () => ({
  createClient: jest.fn(() => mockRedisClient)
}));

describe('RedisClientWrapper 测试', () => {
  const originalEnv = process.env;
  let redisWrapper: RedisClientWrapper;

  beforeEach(() => {
    jest.clearAllMocks();
    // 重置环境变量
    process.env = { ...originalEnv };
    redisWrapper = new RedisClientWrapper();
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('Redis配置检查', () => {
    it('应该在REDIS_URL为空时禁用Redis', () => {
      process.env.REDIS_URL = '';
      const wrapper = new RedisClientWrapper();
      
      expect(wrapper.isAvailable()).toBe(false);
      expect(wrapper.getStatus().enabled).toBe(false);
    });

    it('应该在REDIS_URL未设置时禁用Redis', () => {
      delete process.env.REDIS_URL;
      const wrapper = new RedisClientWrapper();
      
      expect(wrapper.isAvailable()).toBe(false);
      expect(wrapper.getStatus().enabled).toBe(false);
    });

    it('应该在REDIS_URL设置时启用Redis', () => {
      process.env.REDIS_URL = 'redis://localhost:6379';
      const wrapper = new RedisClientWrapper();
      
      expect(wrapper.getStatus().enabled).toBe(true);
    });
  });

  describe('Redis连接管理', () => {
    beforeEach(() => {
      process.env.REDIS_URL = 'redis://localhost:6379';
      redisWrapper = new RedisClientWrapper();
    });

    it('应该成功连接Redis', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);
      
      await redisWrapper.connect();
      
      expect(mockRedisClient.connect).toHaveBeenCalled();
    });

    it('应该在连接失败时优雅降级', async () => {
      mockRedisClient.connect.mockRejectedValue(new Error('连接失败'));
      
      await redisWrapper.connect();
      
      expect(redisWrapper.isAvailable()).toBe(false);
    });

    it('应该正确断开Redis连接', async () => {
      mockRedisClient.quit.mockResolvedValue(undefined);
      mockRedisClient.connect.mockResolvedValue(undefined);

      // 模拟连接状态
      await redisWrapper.connect();
      // 手动设置连接状态
      (redisWrapper as any).isConnected = true;

      await redisWrapper.disconnect();

      expect(mockRedisClient.quit).toHaveBeenCalled();
    });
  });

  describe('缓存操作', () => {
    beforeEach(() => {
      process.env.REDIS_URL = 'redis://localhost:6379';
      redisWrapper = new RedisClientWrapper();
    });

    it('应该在Redis不可用时跳过get操作', async () => {
      const result = await redisWrapper.get('test-key');
      
      expect(result).toBeNull();
      expect(mockRedisClient.get).not.toHaveBeenCalled();
    });

    it('应该在Redis可用时执行get操作', async () => {
      // 模拟Redis可用状态
      mockRedisClient.connect.mockResolvedValue(undefined);
      mockRedisClient.get.mockResolvedValue('test-value');
      await redisWrapper.connect();
      
      // 手动设置连接状态（因为事件监听器在测试中不会触发）
      (redisWrapper as any).isConnected = true;
      
      const result = await redisWrapper.get('test-key');
      
      expect(mockRedisClient.get).toHaveBeenCalledWith('test-key');
      expect(result).toBe('test-value');
    });

    it('应该在Redis不可用时跳过set操作', async () => {
      const result = await redisWrapper.set('test-key', 'test-value');
      
      expect(result).toBe(false);
      expect(mockRedisClient.set).not.toHaveBeenCalled();
    });

    it('应该在Redis可用时执行set操作', async () => {
      // 模拟Redis可用状态
      mockRedisClient.connect.mockResolvedValue(undefined);
      mockRedisClient.set.mockResolvedValue('OK');
      await redisWrapper.connect();
      (redisWrapper as any).isConnected = true;
      
      const result = await redisWrapper.set('test-key', 'test-value');
      
      expect(mockRedisClient.set).toHaveBeenCalledWith('test-key', 'test-value');
      expect(result).toBe(true);
    });

    it('应该在Redis可用时执行带TTL的set操作', async () => {
      // 模拟Redis可用状态
      mockRedisClient.connect.mockResolvedValue(undefined);
      mockRedisClient.setEx.mockResolvedValue('OK');
      await redisWrapper.connect();
      (redisWrapper as any).isConnected = true;
      
      const result = await redisWrapper.set('test-key', 'test-value', 3600);
      
      expect(mockRedisClient.setEx).toHaveBeenCalledWith('test-key', 3600, 'test-value');
      expect(result).toBe(true);
    });

    it('应该在Redis不可用时跳过del操作', async () => {
      const result = await redisWrapper.del('test-key');
      
      expect(result).toBe(false);
      expect(mockRedisClient.del).not.toHaveBeenCalled();
    });

    it('应该在Redis可用时执行del操作', async () => {
      // 模拟Redis可用状态
      mockRedisClient.connect.mockResolvedValue(undefined);
      mockRedisClient.del.mockResolvedValue(1);
      await redisWrapper.connect();
      (redisWrapper as any).isConnected = true;
      
      const result = await redisWrapper.del('test-key');
      
      expect(mockRedisClient.del).toHaveBeenCalledWith('test-key');
      expect(result).toBe(true);
    });

    it('应该在Redis操作失败时优雅处理错误', async () => {
      // 模拟Redis可用状态
      mockRedisClient.connect.mockResolvedValue(undefined);
      mockRedisClient.get.mockRejectedValue(new Error('Redis操作失败'));
      await redisWrapper.connect();
      (redisWrapper as any).isConnected = true;
      
      const result = await redisWrapper.get('test-key');
      
      expect(result).toBeNull();
    });
  });

  describe('健康检查', () => {
    beforeEach(() => {
      process.env.REDIS_URL = 'redis://localhost:6379';
      redisWrapper = new RedisClientWrapper();
    });

    it('应该在Redis不可用时返回false', async () => {
      const result = await redisWrapper.healthCheck();
      
      expect(result).toBe(false);
    });

    it('应该在Redis可用时执行ping检查', async () => {
      // 模拟Redis可用状态
      mockRedisClient.connect.mockResolvedValue(undefined);
      mockRedisClient.ping.mockResolvedValue('PONG');
      await redisWrapper.connect();
      (redisWrapper as any).isConnected = true;
      
      const result = await redisWrapper.healthCheck();
      
      expect(mockRedisClient.ping).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });

  describe('状态信息', () => {
    it('应该返回正确的状态信息', () => {
      process.env.REDIS_URL = 'redis://localhost:6379';
      const wrapper = new RedisClientWrapper();
      
      const status = wrapper.getStatus();
      
      expect(status).toEqual({
        enabled: true,
        connected: false,
        available: false,
        redisUrl: 'redis://localhost:6379'
      });
    });

    it('应该在Redis禁用时返回正确状态', () => {
      process.env.REDIS_URL = '';
      const wrapper = new RedisClientWrapper();
      
      const status = wrapper.getStatus();
      
      expect(status).toEqual({
        enabled: false,
        connected: false,
        available: false,
        redisUrl: undefined
      });
    });
  });
});
