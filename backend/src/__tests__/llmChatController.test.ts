import request from 'supertest';
import express from 'express';
import llmChatRoutes from '../routes/llmChatRoutes';
import { LLMChatService } from '../services/llmChatService';

// Mock LLMChatService
jest.mock('../services/llmChatService');

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  prisma: {}
}));

// Mock auth middleware
jest.mock('../middleware/auth', () => ({
  authenticateJWT: (req: any, res: any, next: any) => next()
}));

const app = express();
app.use(express.json());
app.use('/api/llm', llmChatRoutes);

describe('LLM Chat Controller Integration Tests', () => {
  let mockLLMChatService: jest.Mocked<LLMChatService>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockLLMChatService = LLMChatService.prototype as jest.Mocked<LLMChatService>;
  });

  describe('POST /api/llm/chat', () => {
    const validChatRequest = {
      messages: [
        { role: 'user', content: '你好，请介绍一下你自己' }
      ],
      model: 'deepseek',
      stream: false,
      conversation_id: 'conv_123456',
      options: {
        deep_thinking: true,
        online_search: false
      }
    };

    it('应该成功处理有效的聊天请求', async () => {
      const mockResponse = {
        success: true,
        data: {
          message: {
            role: 'assistant',
            content: '你好！我是AI助手，很高兴为您服务。',
            id: 'msg_123456',
            timestamp: Date.now()
          },
          conversation_id: 'conv_123456',
          message_id: 'msg_123456',
          parent_message_id: 'msg_user_123',
          request_id: 'req_123456',
          model: 'deepseek',
          finish_reason: 'stop',
          usage: {
            prompt_tokens: 20,
            completion_tokens: 30,
            total_tokens: 50
          }
        },
        requestId: 'req_123456',
        timestamp: Date.now()
      };

      mockLLMChatService.processChat = jest.fn().mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/api/llm/chat')
        .send(validChatRequest)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.message.content).toContain('AI助手');
      expect(mockLLMChatService.processChat).toHaveBeenCalledWith(validChatRequest);
    });

    it('应该拒绝缺少必需字段的请求', async () => {
      const invalidRequest = {
        model: 'deepseek',
        stream: false
        // 缺少 messages 字段
      };

      const response = await request(app)
        .post('/api/llm/chat')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('messages');
    });

    it('应该拒绝空消息数组', async () => {
      const invalidRequest = {
        ...validChatRequest,
        messages: []
      };

      const response = await request(app)
        .post('/api/llm/chat')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('不能为空');
    });

    it('应该拒绝不支持的模型', async () => {
      const invalidRequest = {
        ...validChatRequest,
        model: 'unsupported-model'
      };

      const response = await request(app)
        .post('/api/llm/chat')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('不支持的模型');
    });

    it('应该拒绝非布尔值的stream字段', async () => {
      const invalidRequest = {
        ...validChatRequest,
        stream: 'false' // 应该是布尔值
      };

      const response = await request(app)
        .post('/api/llm/chat')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('布尔值');
    });

    it('应该拒绝无效的消息角色', async () => {
      const invalidRequest = {
        ...validChatRequest,
        messages: [
          { role: 'invalid_role', content: '测试消息' }
        ]
      };

      const response = await request(app)
        .post('/api/llm/chat')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('role字段无效');
    });

    it('应该拒绝空内容的消息', async () => {
      const invalidRequest = {
        ...validChatRequest,
        messages: [
          { role: 'user', content: '' }
        ]
      };

      const response = await request(app)
        .post('/api/llm/chat')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('不能为空');
    });

    it('应该拒绝最后消息不是用户消息的请求', async () => {
      const invalidRequest = {
        ...validChatRequest,
        messages: [
          { role: 'user', content: '你好' },
          { role: 'assistant', content: '你好！' }
        ]
      };

      const response = await request(app)
        .post('/api/llm/chat')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('最后一条消息必须是用户消息');
    });

    it('应该验证options字段的类型', async () => {
      const invalidRequest = {
        ...validChatRequest,
        options: {
          temperature: 'invalid', // 应该是数字
          deep_thinking: 'true' // 应该是布尔值
        }
      };

      const response = await request(app)
        .post('/api/llm/chat')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/llm/conversations', () => {
    it('应该返回对话列表', async () => {
      const mockConversations = [
        {
          id: 'conv_1',
          title: '对话1',
          userId: null,
          createdAt: '2023-01-01T00:00:00.000Z',
          updatedAt: '2023-01-01T00:00:00.000Z'
        }
      ];

      mockLLMChatService.getConversations = jest.fn().mockResolvedValue(mockConversations);

      const response = await request(app)
        .get('/api/llm/conversations')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].id).toBe('conv_1');
    });

    it('应该限制返回数量', async () => {
      mockLLMChatService.getConversations = jest.fn().mockResolvedValue([]);

      await request(app)
        .get('/api/llm/conversations?limit=10')
        .expect(200);

      expect(mockLLMChatService.getConversations).toHaveBeenCalledWith(undefined, 10);
    });

    it('应该拒绝过大的limit参数', async () => {
      const response = await request(app)
        .get('/api/llm/conversations?limit=200')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('不能超过100');
    });
  });

  describe('GET /api/llm/conversations/:id/messages', () => {
    it('应该返回对话消息', async () => {
      const mockMessages = [
        {
          id: 'msg_1',
          conversationId: 'conv_123',
          role: 'user',
          content: '你好',
          model: 'deepseek',
          createdAt: '2023-01-01T00:00:00.000Z',
          deepThinking: false,
          onlineSearch: false
        }
      ];

      mockLLMChatService.getConversationMessages = jest.fn().mockResolvedValue(mockMessages);

      const response = await request(app)
        .get('/api/llm/conversations/conv_123/messages')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].content).toBe('你好');
    });

    it('应该拒绝空的对话ID', async () => {
      const response = await request(app)
        .get('/api/llm/conversations//messages')
        .expect(404); // 路由不匹配
    });
  });

  describe('DELETE /api/llm/conversations/:id', () => {
    it('应该成功删除对话', async () => {
      mockLLMChatService.deleteConversation = jest.fn().mockResolvedValue(undefined);

      const response = await request(app)
        .delete('/api/llm/conversations/conv_123')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('删除成功');
      expect(mockLLMChatService.deleteConversation).toHaveBeenCalledWith('conv_123');
    });
  });

  describe('GET /api/llm/models', () => {
    it('应该返回支持的模型列表', async () => {
      const mockModels = ['deepseek', 'gpt-3.5-turbo', 'gpt-4', 'claude-3'];
      mockLLMChatService.getSupportedModels = jest.fn().mockReturnValue(mockModels);

      const response = await request(app)
        .get('/api/llm/models')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockModels);
    });
  });

  describe('GET /api/llm/health', () => {
    it('应该返回健康状态', async () => {
      const mockHealthStatus = {
        'deepseek': true,
        'gpt-3.5-turbo': true,
        'gpt-4': false,
        'claude-3': true
      };

      mockLLMChatService.healthCheck = jest.fn().mockResolvedValue(mockHealthStatus);

      const response = await request(app)
        .get('/api/llm/health')
        .expect(503); // 因为gpt-4为false，所以状态是degraded

      expect(response.body.data.status).toBe('degraded');
      expect(response.body.data.models).toEqual(mockHealthStatus);
    });

    it('应该在所有模型健康时返回200状态', async () => {
      const mockHealthStatus = {
        'deepseek': true,
        'gpt-3.5-turbo': true,
        'gpt-4': true,
        'claude-3': true
      };

      mockLLMChatService.healthCheck = jest.fn().mockResolvedValue(mockHealthStatus);

      const response = await request(app)
        .get('/api/llm/health')
        .expect(200);

      expect(response.body.data.status).toBe('healthy');
    });
  });
});
