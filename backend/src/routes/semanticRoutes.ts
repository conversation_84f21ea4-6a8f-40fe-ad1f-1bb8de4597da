import { Router } from 'express';
import {
  updateTable<PERSON>lias,
  updateColumnAlias,
  createRelationship,
  updateRelationship,
  deleteRelationship,
  getSemanticContext,
  discoverRelationships,
  discoverCrossSourceRelationships,
  discoverAllRelationships,
  applyDiscoveredRelationships
} from '../controllers/semanticController';
import { authenticateJWT } from '../middleware/auth';

const router = Router();

// 获取语义层上下文 - 需要认证
router.get('/context', authenticateJWT, getSemanticContext);

// 更新表别名 - 需要认证
router.put('/tables/:tableId/alias', authenticateJWT, updateTableAlias);

// 更新列别名 - 需要认证
router.put('/columns/:columnId/alias', authenticateJWT, updateColumnAlias);

// 关联关系管理 - 需要认证
router.post('/relationships', authenticateJWT, createRelationship);
router.put('/relationships/:relationshipId', authenticateJWT, updateRelationship);
router.delete('/relationships/:relationshipId', authenticateJWT, deleteRelationship);

// 自动发现关联关系 - 需要认证
router.post('/data-sources/:dataSourceId/discover-relationships', authenticateJWT, discoverRelationships);

// 发现跨数据源关联关系 - 需要认证
router.post('/data-sources/:sourceDataSourceId/discover-cross-relationships', authenticateJWT, discoverCrossSourceRelationships);

// 批量发现所有关联关系 - 需要认证
router.post('/discover-all-relationships', authenticateJWT, discoverAllRelationships);

// 应用发现的关联关系 - 需要认证
router.post('/apply-relationships', authenticateJWT, applyDiscoveredRelationships);

export default router;
