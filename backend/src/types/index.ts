// 数据源类型
export type DataSourceType = 'postgresql' | 'mysql' | 'csv' | 'excel' | 'sqlite';

// 数据源状态
export type DataSourceStatus = 'connected' | 'disconnected' | 'error' | 'syncing';

// 关联关系类型
export type RelationshipType = 'one_to_one' | 'one_to_many' | 'many_to_one';

// 关联关系发现方法
export type RelationshipDiscoveryMethod = 'foreign_key' | 'naming_convention' | 'data_analysis' | 'manual';

// 关联关系置信度等级
export type ConfidenceLevel = 'high' | 'medium' | 'low';

// 关联关系发现结果
export interface RelationshipDiscoveryResult {
  fromColumnId: string;
  toColumnId: string;
  relationshipType: RelationshipType;
  discoveryMethod: RelationshipDiscoveryMethod;
  confidence: ConfidenceLevel;
  confidenceScore: number; // 0-1之间的数值
  evidence: string[]; // 支持该关联关系的证据
  metadata?: Record<string, any>; // 额外的元数据信息
}

// 关联关系发现配置
export interface RelationshipDiscoveryConfig {
  enableForeignKeyDetection: boolean;
  enableNamingConvention: boolean;
  enableDataAnalysis: boolean;
  enableCrossDataSource: boolean;
  namingPatterns: string[];
  confidenceThreshold: number; // 最低置信度阈值
  maxSuggestions: number; // 最大建议数量

  // LLM增强配置
  enableLLMEnhancement?: boolean;
  llmConfig?: LLMEnhancementConfig;
}

// LLM增强配置
export interface LLMEnhancementConfig {
  enableSemanticAnalysis: boolean;
  enableBusinessValidation: boolean;
  enableConfidenceAdjustment: boolean;
  generateExplanations: boolean;
  language: 'zh' | 'en';
  provider: 'openai' | 'azure' | 'anthropic' | 'local';
  model: string;
  temperature: number;
  maxTokens: number;
  batchSize: number;
  enableCache: boolean;
}

// 语义分析结果
export interface SemanticAnalysis {
  semanticSimilarity: number; // 0-1
  businessRelevance: number; // 0-1
  dataTypeCompatibility: number; // 0-1
  suggestedRelationType: RelationshipType;
  explanation: string;
  confidence: ConfidenceLevel;
}

// 业务验证结果
export interface BusinessValidation {
  isBusinessLogical: boolean;
  businessReasoningScore: number; // 0-1
  potentialRisks: string[];
  recommendations: string[];
  domainKnowledgeApplied: string[];
}

// 字段信息（用于LLM分析）
export interface FieldInfo {
  id: string;
  name: string;
  description?: string;
  dataType: string;
  tableName: string;
  sampleData?: any[];
  isPrimaryKey: boolean;
  isNullable: boolean;
  maxLength?: number;
  defaultValue?: any;
}

// 数据源连接配置
export interface DatabaseConnectionConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
}

export interface FileConnectionConfig {
  filename: string;
  path: string;
  encoding?: string;
  delimiter?: string; // for CSV
  sheetName?: string; // for Excel
}

// SQLite 连接配置
export interface SQLiteConnectionConfig {
  filePath: string; // SQLite 数据库文件路径
  mode?: 'readonly' | 'readwrite' | 'create'; // 打开模式
}

export type ConnectionConfig = DatabaseConnectionConfig | FileConnectionConfig | SQLiteConnectionConfig;

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 数据源列表响应
export interface DataSourceListItem {
  id: string;
  name: string;
  type: DataSourceType;
  status: DataSourceStatus;
  createdAt: string;
  updatedAt: string;
}

// 数据表信息
export interface DataTableInfo {
  id: string;
  originalName: string;
  aliasName?: string;
  description?: string;
  columns: DataColumnInfo[];
}

// 数据列信息
export interface DataColumnInfo {
  id: string;
  originalName: string;
  aliasName?: string;
  dataType: string;
  description?: string;
  isPrimaryKey: boolean;
}

// 关联关系信息
export interface DataRelationshipInfo {
  id: string;
  fromColumnId: string;
  toColumnId: string;
  type: RelationshipType;
  isManual: boolean;
  discoveryMethod?: RelationshipDiscoveryMethod;
  confidence?: ConfidenceLevel;
  confidenceScore?: number;
  evidence?: string[];
  metadata?: Record<string, any>;
}

// 数据源Schema详情
export interface DataSourceSchema {
  id: string;
  name: string;
  type: DataSourceType;
  tables: DataTableInfo[];
  relationships: DataRelationshipInfo[];
}

// 聊天请求
export interface ChatRequest {
  sessionId: string;
  prompt: string;
}

// 聊天响应
export interface ChatResponse {
  id: string;
  sessionId: string;
  userPrompt: string;
  responseText: string;
  data?: {
    columns: string[];
    rows: any[][];
  };
  visualization?: {
    type: string;
    title: string;
    spec: any;
  };
  generatedSql?: string;
}

// 可视化图表类型
export type VisualizationType = 'bar_chart' | 'line_chart' | 'pie_chart' | 'table' | 'scatter_plot';

// 错误类型
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 验证错误
export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400);
  }
}

// 数据库错误
export class DatabaseError extends AppError {
  constructor(message: string) {
    super(message, 500);
  }
}

// 外部服务错误
export class ExternalServiceError extends AppError {
  constructor(message: string) {
    super(message, 502);
  }
}

// ==================== 智能报表相关类型定义 ====================

// 用户角色类型
export type UserRole = 'analyst' | 'manager' | 'executive';

// 报表类型
export type ReportType = 'daily' | 'weekly' | 'monthly' | 'custom';

// 报表状态
export type ReportStatus = 'generating' | 'completed' | 'failed' | 'expired';

// 洞察类型
export type InsightType = 'anomaly' | 'trend' | 'opportunity' | 'risk';

// 洞察严重程度
export type InsightSeverity = 'low' | 'medium' | 'high' | 'critical';

// 洞察状态
export type InsightStatus = 'new' | 'acknowledged' | 'resolved';

// 用户配置接口
export interface UserProfile {
  id: string;
  userId: string;
  role: UserRole;
  preferences: {
    language?: string;
    timezone?: string;
    reportFormat?: string;
    notificationSettings?: {
      email?: boolean;
      push?: boolean;
    };
  };
  queryHistory?: {
    recentQueries: string[];
    favoriteTopics: string[];
    analysisPatterns: string[];
  };
  createdAt: string;
  updatedAt: string;
}

// 报表模板接口
export interface ReportTemplate {
  id: string;
  name: string;
  description?: string;
  config: {
    sections: ReportSection[];
    dataSourceIds: string[];
    refreshInterval?: number;
    exportFormats: string[];
  };
  userRole: UserRole;
  domain: string;
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// 报表章节接口
export interface ReportSection {
  id: string;
  title: string;
  type: 'summary' | 'chart' | 'table' | 'insight' | 'text';
  config: {
    query?: string;
    chartType?: string;
    filters?: any[];
    aggregations?: any[];
  };
  order: number;
}

// 报表实例接口
export interface ReportInstance {
  id: string;
  templateId: string;
  userId: string;
  title: string;
  content: {
    executiveSummary: string;
    sections: GeneratedReportSection[];
    keyInsights: DataInsight[];
    actionItems: string[];
    metadata: {
      generationTime: number;
      dataFreshness: string;
      confidence: number;
    };
  };
  metadata?: {
    generationDuration: number;
    dataSourcesUsed: string[];
    aiModelVersion: string;
  };
  status: ReportStatus;
  generatedAt: string;
  expiresAt?: string;
}

// 生成的报表章节
export interface GeneratedReportSection {
  id: string;
  title: string;
  type: string;
  content: {
    text?: string;
    data?: any;
    chart?: {
      type: string;
      spec: any;
    };
    insights?: string[];
  };
  order: number;
}

// 定时任务接口
export interface ScheduledTask {
  id: string;
  templateId: string;
  userId: string;
  scheduleType: ReportType;
  cronExpression: string;
  config: {
    recipients?: string[];
    exportFormat?: string;
    deliveryMethod?: 'email' | 'download' | 'api';
  };
  isActive: boolean;
  lastRun?: string;
  nextRun?: string;
  createdAt: string;
  updatedAt: string;
}

// 数据洞察接口
export interface DataInsight {
  id: string;
  dataSourceId: string;
  type: InsightType;
  title: string;
  description: string;
  confidence: number; // 0-1
  severity: InsightSeverity;
  status: InsightStatus;
  metadata?: {
    affectedMetrics?: string[];
    timeRange?: {
      start: string;
      end: string;
    };
    recommendations?: string[];
    relatedInsights?: string[];
  };
  createdAt: string;
}

// 报表生成请求
export interface ReportGenerationRequest {
  templateId?: string;
  userId: string;
  title?: string;
  config?: {
    dateRange?: {
      start: string;
      end: string;
    };
    dataSourceIds?: string[];
    customSections?: ReportSection[];
  };
  options?: {
    includeInsights?: boolean;
    generateCharts?: boolean;
    exportFormat?: string;
  };
}

// 报表生成响应
export interface ReportGenerationResponse {
  reportId: string;
  status: ReportStatus;
  estimatedCompletionTime?: number;
  downloadUrl?: string;
  previewUrl?: string;
}

// 智能建议接口
export interface IntelligentSuggestion {
  id: string;
  type: 'question' | 'analysis' | 'visualization' | 'insight';
  title: string;
  description: string;
  confidence: number;
  relevanceScore: number;
  metadata?: {
    dataSourceIds?: string[];
    suggestedActions?: string[];
    estimatedValue?: string;
  };
}

// ==================== LLM聊天相关类型定义 ====================

// 消息角色类型
export type LLMMessageRole = 'user' | 'assistant' | 'system';

// 支持的LLM模型类型
export type LLMModelType = 'deepseek' | 'gpt-3.5-turbo' | 'gpt-4' | 'gpt-4-turbo' | 'claude-3' | 'local';

// 完成原因类型
export type LLMFinishReason = 'stop' | 'length' | 'content_filter' | 'tool_calls' | 'function_call';

// LLM聊天消息接口
export interface LLMMessage {
  role: LLMMessageRole;
  content: string;
}

// LLM聊天请求接口
export interface LLMChatRequest {
  messages: LLMMessage[];
  model: LLMModelType;
  stream: boolean;
  conversation_id?: string;
  options?: {
    deep_thinking?: boolean;
    online_search?: boolean;
    temperature?: number;
    max_tokens?: number;
  };
}

// LLM使用量统计接口
export interface LLMUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

// LLM聊天响应消息接口
export interface LLMResponseMessage {
  role: 'assistant';
  content: string;
  id: string;
  timestamp: number;
}

// LLM聊天响应接口
export interface LLMChatResponse {
  success: boolean;
  data: {
    message: LLMResponseMessage;
    conversation_id: string;
    message_id: string;
    parent_message_id?: string;
    request_id: string;
    model: LLMModelType;
    finish_reason: LLMFinishReason;
    usage: LLMUsage;
  };
  requestId: string;
  timestamp: number;
}

// LLM对话会话接口
export interface LLMConversation {
  id: string;
  title?: string;
  userId?: string;
  createdAt: string;
  updatedAt: string;
  messages?: LLMMessageRecord[];
}

// LLM消息记录接口（数据库存储格式）
export interface LLMMessageRecord {
  id: string;
  conversationId: string;
  role: LLMMessageRole;
  content: string;
  model?: LLMModelType;
  parentId?: string;
  requestId?: string;
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;
  finishReason?: LLMFinishReason;
  deepThinking: boolean;
  onlineSearch: boolean;
  metadata?: any;
  createdAt: string;
}

// LLM客户端配置接口
export interface LLMClientConfig {
  provider: 'openai' | 'azure' | 'anthropic' | 'deepseek' | 'local';
  model: LLMModelType;
  apiKey?: string;
  baseUrl?: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
}

// LLM服务错误类型
export class LLMServiceError extends AppError {
  constructor(message: string, statusCode: number = 500) {
    super(message, statusCode);
  }
}
