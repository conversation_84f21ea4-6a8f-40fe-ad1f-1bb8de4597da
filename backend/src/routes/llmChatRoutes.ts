import { Router } from 'express';
import {
  processLLMChat,
  getConversations,
  getConversationMessages,
  createConversation,
  deleteConversation,
  getSupportedModels,
  llmHealthCheck
} from '../controllers/llmChatController';
import { authenticateJWT } from '../middleware/auth';
import {
  validateLLMChatRequest,
  rateLimitMiddleware,
  contentSecurityMiddleware
} from '../middleware/llmValidation';
import { llmErrorHandler, errorStatsMiddleware } from '../middleware/llmErrorHandler';

const router = Router();

/**
 * LLM聊天相关路由
 * 基础路径: /api/llm
 */

// 应用全局中间件
router.use(errorStatsMiddleware);

// 发送聊天消息 - 核心接口
router.post('/chat',
  rateLimitMiddleware,
  authenticateJWT,
  contentSecurityMiddleware,
  validateLLMChatRequest,
  processLLMChat
);

// 对话管理
router.get('/conversations', authenticateJWT, getConversations);
router.post('/conversations', authenticateJWT, createConversation);
router.get('/conversations/:id/messages', authenticateJWT, getConversationMessages);
router.delete('/conversations/:id', authenticateJWT, deleteConversation);

// 模型和健康检查
router.get('/models', authenticateJWT, getSupportedModels);
router.get('/health', llmHealthCheck); // 健康检查不需要认证

// 错误统计接口（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  router.get('/error-stats', (req, res) => {
    const { getErrorStats } = require('../middleware/llmErrorHandler');
    res.json(getErrorStats());
  });
}

// 应用LLM专用错误处理
router.use(llmErrorHandler);

export default router;
