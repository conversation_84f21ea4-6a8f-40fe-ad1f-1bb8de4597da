import { PrismaClient } from '@prisma/client';
import {
  DataSourceType,
  RelationshipDiscoveryResult,
  RelationshipDiscoveryConfig,
  LLMEnhancementConfig,
  FieldInfo,
  AppError,
  DatabaseError
} from '../../types';
import { RelationshipDiscoveryService } from '../relationshipDiscoveryService';
import { LLMSemanticAnalyzer } from './llmSemanticAnalyzer';
import { LLMBusinessValidator } from './llmBusinessValidator';
import { LLMDomainAdapter } from './llmDomainAdapter';
import { LLMExplanationGenerator } from './llmExplanationGenerator';
import { LLMConfidenceAdjuster, BusinessContext } from './llmConfidenceAdjuster';

/**
 * 增强的关联关系发现结果
 */
export interface EnhancedRelationshipDiscoveryResult extends RelationshipDiscoveryResult {
  // LLM增强信息
  semanticAnalysis?: any;
  businessValidation?: any;
  domainAnalysis?: any;
  explanation?: any;
  confidenceAdjustment?: any;
  enhancedBy: 'llm' | 'traditional';
  processingTime?: number;
}

/**
 * LLM增强的关联关系发现服务
 * 集成多个LLM组件，提供智能化的关联关系发现能力
 */
export class EnhancedRelationshipDiscoveryService extends RelationshipDiscoveryService {
  private llmSemanticAnalyzer: LLMSemanticAnalyzer;
  private llmBusinessValidator: LLMBusinessValidator;
  private llmDomainAdapter: LLMDomainAdapter;
  private llmExplanationGenerator: LLMExplanationGenerator;
  private llmConfidenceAdjuster: LLMConfidenceAdjuster;
  private llmConfig: LLMEnhancementConfig;

  constructor(prisma: PrismaClient, llmConfig: LLMEnhancementConfig) {
    super(prisma);
    this.llmConfig = llmConfig;
    
    // 初始化LLM组件
    this.llmSemanticAnalyzer = new LLMSemanticAnalyzer(llmConfig);
    this.llmBusinessValidator = new LLMBusinessValidator(llmConfig);
    this.llmDomainAdapter = new LLMDomainAdapter(llmConfig);
    this.llmExplanationGenerator = new LLMExplanationGenerator(llmConfig);
    this.llmConfidenceAdjuster = new LLMConfidenceAdjuster(llmConfig);
  }

  /**
   * 使用LLM增强发现关联关系
   */
  async discoverRelationships(
    dataSourceId: string,
    config?: Partial<RelationshipDiscoveryConfig>
  ): Promise<EnhancedRelationshipDiscoveryResult[]> {
    const startTime = Date.now();
    
    try {
      // 1. 使用传统方法发现关联关系
      const traditionalResults = await super.discoverRelationships(dataSourceId, config);
      
      // 2. 如果未启用LLM增强，直接返回传统结果
      if (!config?.enableLLMEnhancement || !this.llmConfig) {
        return traditionalResults.map(result => ({
          ...result,
          enhancedBy: 'traditional' as const,
          processingTime: Date.now() - startTime
        }));
      }

      // 3. 获取字段信息用于LLM分析
      const fieldInfoMap = await this.getFieldInfoMap(traditionalResults);
      
      // 4. 获取业务上下文
      const businessContext = await this.getBusinessContext(dataSourceId);

      // 5. 使用LLM增强结果
      const enhancedResults = await this.enhanceRelationshipsWithLLM(
        traditionalResults,
        fieldInfoMap,
        businessContext
      );

      // 6. 重新排序和过滤增强后的结果
      const finalResults = this.reRankEnhancedResults(enhancedResults, config);

      return finalResults.map(result => ({
        ...result,
        processingTime: Date.now() - startTime
      }));

    } catch (error) {
      if (error instanceof AppError) throw error;
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new DatabaseError(`LLM增强关联关系发现失败: ${errorMessage}`);
    }
  }

  /**
   * 使用LLM增强关联关系列表
   */
  private async enhanceRelationshipsWithLLM(
    relationships: RelationshipDiscoveryResult[],
    fieldInfoMap: Map<string, FieldInfo>,
    businessContext: BusinessContext
  ): Promise<EnhancedRelationshipDiscoveryResult[]> {
    const enhancedResults: EnhancedRelationshipDiscoveryResult[] = [];

    // 准备批量处理的数据
    const relationshipData = relationships.map(relationship => ({
      relationship,
      fromField: fieldInfoMap.get(relationship.fromColumnId)!,
      toField: fieldInfoMap.get(relationship.toColumnId)!
    })).filter(item => item.fromField && item.toField);

    // 1. 批量语义分析
    let semanticAnalyses: any[] = [];
    if (this.llmConfig.enableSemanticAnalysis) {
      try {
        const fieldPairs = relationshipData.map(item => ({
          field1: item.fromField,
          field2: item.toField
        }));
        semanticAnalyses = await this.llmSemanticAnalyzer.analyzeBatchSemanticSimilarity(fieldPairs);
      } catch (error) {
        console.error('批量语义分析失败:', error);
        semanticAnalyses = relationshipData.map(() => null);
      }
    }

    // 2. 批量业务验证
    let businessValidations: any[] = [];
    if (this.llmConfig.enableBusinessValidation) {
      try {
        businessValidations = await this.llmBusinessValidator.validateBatchBusinessLogic(
          relationshipData,
          businessContext
        );
      } catch (error) {
        console.error('批量业务验证失败:', error);
        businessValidations = relationshipData.map(() => null);
      }
    }

    // 3. 批量置信度调整
    let confidenceAdjustments: any[] = [];
    if (this.llmConfig.enableConfidenceAdjustment) {
      try {
        const adjustmentData = relationshipData.map((item, index) => ({
          ...item,
          semanticAnalysis: semanticAnalyses[index],
          businessValidation: businessValidations[index]
        }));
        confidenceAdjustments = await this.llmConfidenceAdjuster.adjustBatchConfidence(
          adjustmentData,
          businessContext
        );
      } catch (error) {
        console.error('批量置信度调整失败:', error);
        confidenceAdjustments = relationshipData.map(() => null);
      }
    }

    // 4. 批量生成解释
    let explanations: any[] = [];
    if (this.llmConfig.generateExplanations) {
      try {
        const explanationData = relationshipData.map((item, index) => ({
          ...item,
          semanticAnalysis: semanticAnalyses[index],
          businessValidation: businessValidations[index]
        }));
        explanations = await this.llmExplanationGenerator.generateBatchExplanations(
          explanationData,
          'general'
        );
      } catch (error) {
        console.error('批量解释生成失败:', error);
        explanations = relationshipData.map(() => null);
      }
    }

    // 5. 组合增强结果
    for (let i = 0; i < relationshipData.length; i++) {
      const item = relationshipData[i];
      const semanticAnalysis = semanticAnalyses[i];
      const businessValidation = businessValidations[i];
      const confidenceAdjustment = confidenceAdjustments[i];
      const explanation = explanations[i];

      // 计算最终置信度
      const finalConfidenceScore = confidenceAdjustment?.adjustedScore || 
                                   item.relationship.confidenceScore || 0;
      const finalConfidence = this.scoreToConfidenceLevel(finalConfidenceScore);

      const enhancedResult: EnhancedRelationshipDiscoveryResult = {
        ...item.relationship,
        confidenceScore: finalConfidenceScore,
        confidence: finalConfidence,
        semanticAnalysis,
        businessValidation,
        explanation,
        confidenceAdjustment,
        enhancedBy: 'llm',
        
        // 更新证据列表
        evidence: [
          ...(item.relationship.evidence || []),
          ...(semanticAnalysis ? [`语义相似性: ${semanticAnalysis.semanticSimilarity.toFixed(2)}`] : []),
          ...(businessValidation ? [`业务合理性: ${businessValidation.isBusinessLogical ? '是' : '否'}`] : [])
        ],

        // 更新元数据
        metadata: {
          ...(item.relationship.metadata || {}),
          llmEnhanced: true,
          semanticScore: semanticAnalysis?.semanticSimilarity,
          businessScore: businessValidation?.businessReasoningScore,
          originalConfidence: item.relationship.confidenceScore,
          adjustmentReason: confidenceAdjustment?.adjustmentReason
        }
      };

      enhancedResults.push(enhancedResult);
    }

    return enhancedResults;
  }

  /**
   * 获取字段信息映射
   */
  private async getFieldInfoMap(
    relationships: RelationshipDiscoveryResult[]
  ): Promise<Map<string, FieldInfo>> {
    const columnIds = new Set<string>();
    relationships.forEach(rel => {
      columnIds.add(rel.fromColumnId);
      columnIds.add(rel.toColumnId);
    });

    const columns = await this.prisma.dataColumn.findMany({
      where: { id: { in: Array.from(columnIds) } },
      include: { dataTable: true }
    });

    const fieldInfoMap = new Map<string, FieldInfo>();
    
    for (const column of columns) {
      // 获取样本数据
      const sampleData = await this.getSampleData(column.id);
      
      const fieldInfo: FieldInfo = {
        id: column.id,
        name: column.originalColumnName,
        description: column.description || undefined,
        dataType: column.originalDataType,
        tableName: column.dataTable.originalTableName,
        sampleData,
        isPrimaryKey: column.isPrimaryKey,
        isNullable: true, // 默认为可空，因为数据库模型中没有这个字段
        maxLength: undefined, // 数据库模型中没有这个字段
        defaultValue: undefined // 数据库模型中没有这个字段
      };
      
      fieldInfoMap.set(column.id, fieldInfo);
    }

    return fieldInfoMap;
  }

  /**
   * 获取样本数据
   */
  private async getSampleData(columnId: string): Promise<any[]> {
    try {
      // 这里应该根据实际的数据源类型获取样本数据
      // 暂时返回空数组，实际实现需要连接到数据源
      return [];
    } catch (error) {
      console.error('获取样本数据失败:', error);
      return [];
    }
  }

  /**
   * 获取业务上下文
   */
  private async getBusinessContext(dataSourceId: string): Promise<BusinessContext> {
    try {
      const dataSource = await this.prisma.dataSource.findUnique({
        where: { id: dataSourceId }
      });

      if (!dataSource) {
        throw new Error('数据源不存在');
      }

      // 获取历史反馈数据
      const historicalFeedback = await this.getHistoricalFeedback(dataSourceId);

      return {
        industry: 'general', // 可以从数据源配置中获取
        dataSourceTypes: [dataSource.type],
        systemType: 'ai-bim',
        userFeedback: historicalFeedback,
        historicalAccuracy: await this.calculateHistoricalAccuracy(dataSourceId)
      };
    } catch (error) {
      console.error('获取业务上下文失败:', error);
      return {
        industry: 'general',
        dataSourceTypes: ['unknown'],
        systemType: 'ai-bim'
      };
    }
  }

  /**
   * 获取历史反馈数据
   */
  private async getHistoricalFeedback(dataSourceId: string): Promise<any[]> {
    // 这里应该从反馈表中获取历史数据
    // 暂时返回空数组
    return [];
  }

  /**
   * 计算历史准确率
   */
  private async calculateHistoricalAccuracy(dataSourceId: string): Promise<any> {
    // 这里应该计算各种方法和置信度等级的历史准确率
    // 暂时返回默认值
    return {
      byMethod: {
        foreign_key: 0.95,
        naming_convention: 0.7,
        data_analysis: 0.6
      },
      byConfidenceLevel: {
        high: 0.9,
        medium: 0.7,
        low: 0.4
      }
    };
  }

  /**
   * 重新排序增强后的结果
   */
  private reRankEnhancedResults(
    results: EnhancedRelationshipDiscoveryResult[],
    config?: Partial<RelationshipDiscoveryConfig>
  ): EnhancedRelationshipDiscoveryResult[] {
    // 按增强后的置信度排序
    results.sort((a, b) => {
      // 优先考虑LLM增强的结果
      if (a.enhancedBy === 'llm' && b.enhancedBy !== 'llm') return -1;
      if (b.enhancedBy === 'llm' && a.enhancedBy !== 'llm') return 1;
      
      // 然后按置信度排序
      return (b.confidenceScore || 0) - (a.confidenceScore || 0);
    });

    // 应用配置的过滤条件
    const threshold = config?.confidenceThreshold || 0.3;
    const maxSuggestions = config?.maxSuggestions || 50;

    return results
      .filter(result => (result.confidenceScore || 0) >= threshold)
      .slice(0, maxSuggestions);
  }

  /**
   * 将分数转换为置信度等级
   */
  private scoreToConfidenceLevel(score: number): 'high' | 'medium' | 'low' {
    if (score >= 0.8) return 'high';
    if (score >= 0.5) return 'medium';
    return 'low';
  }

  /**
   * 获取LLM增强统计信息
   */
  async getLLMEnhancementStats(): Promise<{
    cacheStats: {
      semantic: { size: number };
      business: { size: number };
      explanation: { size: number };
      confidence: { size: number };
    };
    tokenUsage: { total: number; daily: number };
    performanceMetrics: {
      averageProcessingTime: number;
      enhancementSuccessRate: number;
    };
  }> {
    return {
      cacheStats: {
        semantic: this.llmSemanticAnalyzer.getCacheStats(),
        business: this.llmBusinessValidator.getCacheStats(),
        explanation: this.llmExplanationGenerator.getCacheStats(),
        confidence: this.llmConfidenceAdjuster.getCacheStats()
      },
      tokenUsage: this.llmSemanticAnalyzer['llmClient'].getTokenUsage(),
      performanceMetrics: {
        averageProcessingTime: 0, // 需要实现统计逻辑
        enhancementSuccessRate: 0 // 需要实现统计逻辑
      }
    };
  }

  /**
   * 清除所有LLM缓存
   */
  clearAllLLMCaches(): void {
    this.llmSemanticAnalyzer.clearCache();
    this.llmBusinessValidator.clearCache();
    this.llmDomainAdapter.clearCache();
    this.llmExplanationGenerator.clearCache();
    this.llmConfidenceAdjuster.clearCache();
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    traditional: boolean;
    llm: boolean;
    overall: boolean;
  }> {
    const traditionalHealthy = true; // 基础服务健康检查
    const llmHealthy = await this.llmSemanticAnalyzer['llmClient'].healthCheck();
    
    return {
      traditional: traditionalHealthy,
      llm: llmHealthy,
      overall: traditionalHealthy && llmHealthy
    };
  }
}
