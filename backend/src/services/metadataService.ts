import { PrismaClient } from '@prisma/client';
import { Client as PgClient } from 'pg';
import mysql from 'mysql2/promise';
import sqlite3 from 'sqlite3';
import fs from 'fs/promises';
import { createReadStream } from 'fs';
import csv from 'csv-parser';
import * as XLSX from 'xlsx';
import { Readable } from 'stream';

import {
  DataSourceType,
  DatabaseConnectionConfig,
  FileConnectionConfig,
  SQLiteConnectionConfig,
  AppError,
  DatabaseError
} from '../types';
import { decryptConnectionConfig } from '../utils/encryption';

export class MetadataService {
  constructor(private prisma: PrismaClient) {}

  /**
   * 同步数据源元数据
   */
  async syncMetadata(dataSourceId: string): Promise<void> {
    try {
      const dataSource = await this.prisma.dataSource.findUnique({
        where: { id: dataSourceId }
      });

      if (!dataSource) {
        throw new AppError('数据源不存在', 404);
      }

      const config = decryptConnectionConfig(dataSource.connectionConfig);
      const type = dataSource.type as DataSourceType;

      let metadata: any;
      switch (type) {
        case 'postgresql':
          metadata = await this.extractPostgreSQLMetadata(config);
          break;
        case 'mysql':
          metadata = await this.extractMySQLMetadata(config);
          break;
        case 'sqlite':
          metadata = await this.extractSQLiteMetadata(config);
          break;
        case 'csv':
          metadata = await this.extractCSVMetadata(config);
          break;
        case 'excel':
          metadata = await this.extractExcelMetadata(config);
          break;
        default:
          throw new AppError('不支持的数据源类型', 400);
      }

      // 保存元数据到数据库
      await this.saveMetadata(dataSourceId, metadata);

      // 自动发现关联关系（仅对数据库类型）
      if (type === 'postgresql' || type === 'mysql' || type === 'sqlite') {
        await this.discoverDatabaseRelationships(dataSourceId, config, type);
      }

    } catch (error) {
      if (error instanceof AppError) throw error;
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new DatabaseError(`元数据同步失败: ${errorMessage}`);
    }
  }

  /**
   * 提取PostgreSQL元数据
   */
  private async extractPostgreSQLMetadata(config: DatabaseConnectionConfig): Promise<any> {
    const client = new PgClient({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl
    });

    try {
      await client.connect();

      // 获取表信息
      const tablesResult = await client.query(`
        SELECT 
          table_name,
          table_comment
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
      `);

      const tables = [];
      for (const table of tablesResult.rows) {
        // 获取列信息
        const columnsResult = await client.query(`
          SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default,
            character_maximum_length,
            numeric_precision,
            numeric_scale
          FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = $1
          ORDER BY ordinal_position
        `, [table.table_name]);

        // 获取主键信息
        const primaryKeysResult = await client.query(`
          SELECT column_name
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
          WHERE tc.table_schema = 'public'
          AND tc.table_name = $1
          AND tc.constraint_type = 'PRIMARY KEY'
        `, [table.table_name]);

        const primaryKeys = primaryKeysResult.rows.map(row => row.column_name);

        const columns = columnsResult.rows.map(column => ({
          name: column.column_name,
          dataType: this.normalizeDataType(column.data_type),
          isNullable: column.is_nullable === 'YES',
          isPrimaryKey: primaryKeys.includes(column.column_name),
          defaultValue: column.column_default,
          maxLength: column.character_maximum_length,
          precision: column.numeric_precision,
          scale: column.numeric_scale
        }));

        tables.push({
          name: table.table_name,
          comment: table.table_comment,
          columns
        });
      }

      return { tables };
    } finally {
      await client.end();
    }
  }

  /**
   * 提取MySQL元数据
   */
  private async extractMySQLMetadata(config: DatabaseConnectionConfig): Promise<any> {
    const connection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl ? { rejectUnauthorized: false } : undefined
    });

    try {
      // 获取表信息
      const [tablesResult] = await connection.execute(`
        SELECT 
          table_name,
          table_comment
        FROM information_schema.tables 
        WHERE table_schema = ?
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
      `, [config.database]);

      const tables = [];
      for (const table of tablesResult as any[]) {
        // 获取列信息
        const [columnsResult] = await connection.execute(`
          SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default,
            character_maximum_length,
            numeric_precision,
            numeric_scale,
            column_key
          FROM information_schema.columns 
          WHERE table_schema = ?
          AND table_name = ?
          ORDER BY ordinal_position
        `, [config.database, table.table_name]);

        const columns = (columnsResult as any[]).map(column => ({
          name: column.column_name,
          dataType: this.normalizeDataType(column.data_type),
          isNullable: column.is_nullable === 'YES',
          isPrimaryKey: column.column_key === 'PRI',
          defaultValue: column.column_default,
          maxLength: column.character_maximum_length,
          precision: column.numeric_precision,
          scale: column.numeric_scale
        }));

        tables.push({
          name: table.table_name,
          comment: table.table_comment,
          columns
        });
      }

      return { tables };
    } finally {
      await connection.end();
    }
  }

  /**
   * 提取 SQLite 元数据
   */
  private async extractSQLiteMetadata(config: SQLiteConnectionConfig): Promise<any> {
    return new Promise((resolve, reject) => {
      // 确定 SQLite 打开模式
      let mode = sqlite3.OPEN_READONLY; // 元数据提取只需要只读权限

      const db = new sqlite3.Database(config.filePath, mode, (err) => {
        if (err) {
          reject(new DatabaseError(`SQLite 连接失败: ${err.message}`));
          return;
        }

        // 获取所有表信息
        db.all(`
          SELECT name, sql
          FROM sqlite_master
          WHERE type = 'table'
          AND name NOT LIKE 'sqlite_%'
          ORDER BY name
        `, (err, tables) => {
          if (err) {
            db.close();
            reject(new DatabaseError(`获取 SQLite 表信息失败: ${err.message}`));
            return;
          }

          const tablePromises = (tables as any[]).map(table => {
            return new Promise((resolveTable, rejectTable) => {
              // 获取表的列信息
              db.all(`PRAGMA table_info(${table.name})`, (err, columns) => {
                if (err) {
                  rejectTable(new DatabaseError(`获取表 ${table.name} 列信息失败: ${err.message}`));
                  return;
                }

                const tableColumns = (columns as any[]).map(column => ({
                  name: column.name,
                  dataType: column.type || 'TEXT',
                  isNullable: column.notnull === 0,
                  defaultValue: column.dflt_value,
                  isPrimaryKey: column.pk === 1
                }));

                resolveTable({
                  name: table.name,
                  comment: `SQLite 表: ${table.name}`,
                  columns: tableColumns
                });
              });
            });
          });

          Promise.all(tablePromises)
            .then(tablesData => {
              db.close((closeErr) => {
                if (closeErr) {
                  console.warn('SQLite 数据库关闭时出现警告:', closeErr.message);
                }
              });

              resolve({ tables: tablesData });
            })
            .catch(error => {
              db.close();
              reject(error);
            });
        });
      });
    });
  }

  /**
   * 提取CSV元数据
   */
  private async extractCSVMetadata(config: FileConnectionConfig): Promise<any> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];
      const columns: any[] = [];
      let isFirstRow = true;

      createReadStream(config.path)
        .pipe(csv({ 
          separator: config.delimiter || ',',
          headers: true
        }))
        .on('headers', (headers: string[]) => {
          headers.forEach((header: string) => {
            columns.push({
              name: header,
              dataType: 'text', // CSV默认都是文本类型
              isNullable: true,
              isPrimaryKey: false
            });
          });
        })
        .on('data', (data: any) => {
          if (results.length < 100) { // 只读取前100行用于类型推断
            results.push(data);
          }
        })
        .on('end', () => {
          // 简单的类型推断
          this.inferCSVColumnTypes(columns, results);
          
          resolve({
            tables: [{
              name: config.filename.replace(/\.[^/.]+$/, ""), // 移除文件扩展名
              comment: `CSV文件: ${config.filename}`,
              columns
            }]
          });
        })
        .on('error', reject);
    });
  }

  /**
   * 提取Excel元数据
   */
  private async extractExcelMetadata(config: FileConnectionConfig): Promise<any> {
    const workbook = XLSX.readFile(config.path);
    const tables = [];

    for (const sheetName of workbook.SheetNames) {
      if (config.sheetName && sheetName !== config.sheetName) {
        continue; // 如果指定了工作表名，只处理指定的工作表
      }

      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      if (jsonData.length === 0) continue;

      const headers = jsonData[0] as string[];
      const dataRows = jsonData.slice(1, 101); // 取前100行用于类型推断

      const columns = headers.map(header => ({
        name: header,
        dataType: 'text',
        isNullable: true,
        isPrimaryKey: false
      }));

      // 简单的类型推断
      this.inferExcelColumnTypes(columns, dataRows);

      tables.push({
        name: config.sheetName || sheetName,
        comment: `Excel工作表: ${sheetName}`,
        columns
      });
    }

    return { tables };
  }

  /**
   * 保存元数据到数据库
   */
  private async saveMetadata(dataSourceId: string, metadata: any): Promise<void> {
    // 删除旧的元数据
    await this.prisma.dataTable.deleteMany({
      where: { dataSourceId }
    });

    // 保存新的元数据
    for (const table of metadata.tables) {
      const dataTable = await this.prisma.dataTable.create({
        data: {
          dataSourceId,
          originalTableName: table.name,
          description: table.comment,
          isSynced: true
        }
      });

      for (const column of table.columns) {
        await this.prisma.dataColumn.create({
          data: {
            dataTableId: dataTable.id,
            originalColumnName: column.name,
            originalDataType: column.dataType,
            isPrimaryKey: column.isPrimaryKey
          }
        });
      }
    }
  }

  /**
   * 自动发现数据库关联关系
   */
  private async discoverDatabaseRelationships(
    dataSourceId: string, 
    config: DatabaseConnectionConfig, 
    type: DataSourceType
  ): Promise<void> {
    // 这里可以实现更复杂的关联关系发现逻辑
    // 例如通过外键约束、命名约定等
    
    // 暂时实现简单的命名约定发现
    const tables = await this.prisma.dataTable.findMany({
      where: { dataSourceId },
      include: { dataColumns: true }
    });

    for (const table of tables) {
      for (const column of table.dataColumns) {
        if (column.isPrimaryKey) {
          // 查找可能的外键
          const potentialForeignKeys = await this.findPotentialForeignKeys(
            column.originalColumnName,
            table.id,
            tables
          );

          for (const fk of potentialForeignKeys) {
            // 检查是否已存在关联关系
            const existingRelationship = await this.prisma.dataRelationship.findFirst({
              where: {
                OR: [
                  { fromColumnId: column.id, toColumnId: fk.id },
                  { fromColumnId: fk.id, toColumnId: column.id }
                ]
              }
            });

            if (!existingRelationship) {
              await this.prisma.dataRelationship.create({
                data: {
                  fromColumnId: column.id,
                  toColumnId: fk.id,
                  relationshipType: 'one_to_many',
                  isManual: false,
                  evidence: JSON.stringify(['命名约定匹配'])
                }
              });
            }
          }
        }
      }
    }
  }

  // 辅助方法
  private normalizeDataType(dataType: string): string {
    const type = dataType.toLowerCase();
    if (type.includes('int') || type.includes('serial')) return 'integer';
    if (type.includes('decimal') || type.includes('numeric') || type.includes('float') || type.includes('double')) return 'decimal';
    if (type.includes('bool')) return 'boolean';
    if (type.includes('date') || type.includes('time')) return 'datetime';
    if (type.includes('text') || type.includes('char') || type.includes('varchar')) return 'text';
    return 'text';
  }

  private inferCSVColumnTypes(columns: any[], data: any[]): void {
    columns.forEach((column, index) => {
      const values = data.map(row => row[column.name]).filter(val => val !== null && val !== '');
      
      if (values.length === 0) return;

      // 检查是否为数字
      const isNumeric = values.every(val => !isNaN(Number(val)));
      if (isNumeric) {
        const hasDecimals = values.some(val => val.toString().includes('.'));
        column.dataType = hasDecimals ? 'decimal' : 'integer';
        return;
      }

      // 检查是否为日期
      const isDate = values.every(val => !isNaN(Date.parse(val)));
      if (isDate) {
        column.dataType = 'datetime';
        return;
      }

      // 默认为文本
      column.dataType = 'text';
    });
  }

  private inferExcelColumnTypes(columns: any[], data: any[]): void {
    columns.forEach((column, colIndex) => {
      const values = data.map(row => row[colIndex]).filter(val => val !== null && val !== undefined && val !== '');
      
      if (values.length === 0) return;

      // 检查是否为数字
      const isNumeric = values.every(val => typeof val === 'number' || !isNaN(Number(val)));
      if (isNumeric) {
        const hasDecimals = values.some(val => val.toString().includes('.'));
        column.dataType = hasDecimals ? 'decimal' : 'integer';
        return;
      }

      // 检查是否为日期
      const isDate = values.every(val => val instanceof Date || !isNaN(Date.parse(val)));
      if (isDate) {
        column.dataType = 'datetime';
        return;
      }

      // 默认为文本
      column.dataType = 'text';
    });
  }

  private async findPotentialForeignKeys(primaryKeyName: string, excludeTableId: string, allTables: any[]): Promise<any[]> {
    const potentialForeignKeys = [];
    
    // 简单的命名约定匹配
    const patterns = [
      primaryKeyName, // 完全匹配
      `${primaryKeyName.replace('_id', '')}_id`, // 如果主键是 id，查找 table_id
      `${primaryKeyName.replace('id', '')}_id` // 其他变体
    ];

    for (const table of allTables) {
      if (table.id === excludeTableId) continue;
      
      for (const column of table.dataColumns) {
        if (patterns.some(pattern => column.originalColumnName.toLowerCase().includes(pattern.toLowerCase()))) {
          potentialForeignKeys.push(column);
        }
      }
    }

    return potentialForeignKeys;
  }
}
