# 项目: AI-BI 商业智能系统

## 一般说明

- 创建新模块或者新的类时, 请在 `docs` 目录下新建对应的中文说明文档 (Markdown 格式)。
- 关键的代码和配置必须有详细的中文注释, 解释其功能和设计原因。
- 所有对外暴露的 API 接口和核心服务方法, 都需要有方法级别的中文注释, 清晰说明其功能、入参和出参。
- 系统输出的日志应采用中文, 以便快速定位和解决问题。

## 工作流程

- **任务分析**: 在开始编码前, 充分分析需求, 对于复杂任务, 必须先编写简要的设计或计划文档。
- **测试驱动**:
    - 编写工具类或核心服务时, 需要添加完整的单元测试。
    - 编写新的 API 端点或功能时, 应编写相应的集成测试, 确保端到端逻辑正确。
- **代码提交**:
    - 功能完成后, 总结本次修改的核心内容。
    - 以中文编写清晰、规范的 Git Commit Message, 然后提交代码。
- **文档同步**: 如果代码变更影响了系统架构或 API, 请同步更新 `docs` 目录下的相关文档。

## 大模型集成

系统通过内部的 **LLM (大语言模型) 集成层** 来增强核心功能, 而非直接调用固定的 API 端点。

### 核心能力
- **语义理解增强**: 利用 LLM 深度理解数据列的业务含义, 识别同义词、缩写和多语言混合字段。
- **业务逻辑推理**: 基于业务知识库对自动发现的关联关系进行逻辑校验, 判断其合理性。
- **智能置信度评估**: 结合语义分析和业务逻辑, 动态调整关联关系的置信度分数。
- **自然语言解释**: 为最终用户生成关于数据关联关系的、通俗易懂的中文解释。

### 技术实现
- **抽象接口**: 系统定义了如 `LLMRelationshipEnhancer` 等抽象服务接口, 以统一的方式调用大模型能力。
- **配置化**: 大模型的具体提供商 (如 OpenAI, Azure, 本地模型等)、API Key 和模型名称 (如 GPT-4) 均通过环境变量进行配置, 方便切换和管理。
- **功能开关**: 所有 LLM 增强功能都配有功能开关, 可以在不同环境中（开发、生产）灵活启用或禁用, 以控制成本和性能。