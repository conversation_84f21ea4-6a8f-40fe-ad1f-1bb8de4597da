import * as XLSX from 'xlsx';
import csv from 'csv-parser';
import fs from 'fs/promises';
import { createReadStream } from 'fs';
import { Readable } from 'stream';
import { PrismaClient } from '@prisma/client';

import {
  DataSourceType,
  ConnectionConfig,
  FileConnectionConfig,
  RelationshipDiscoveryResult,
  RelationshipDiscoveryConfig,
  RelationshipType,
  ConfidenceLevel,
  RelationshipDiscoveryMethod
} from '../../types';
import { IRelationshipDiscovery } from '../../interfaces/relationshipDiscovery';

/**
 * 文件关联关系发现器
 * 支持CSV和Excel文件的关联关系发现
 */
export class FileRelationshipDiscovery implements IRelationshipDiscovery {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient, public readonly dataSourceType: DataSourceType) {
    this.prisma = prisma;
    if (dataSourceType !== 'csv' && dataSourceType !== 'excel') {
      throw new Error(`不支持的文件类型: ${dataSourceType}`);
    }
  }

  /**
   * 发现数据源内部的关联关系
   */
  async discoverInternalRelationships(
    dataSourceId: string,
    config: ConnectionConfig,
    discoveryConfig: RelationshipDiscoveryConfig
  ): Promise<RelationshipDiscoveryResult[]> {
    const results: RelationshipDiscoveryResult[] = [];
    const fileConfig = config as FileConnectionConfig;

    // 对于单个文件，内部关联关系较少，主要是自引用
    if (discoveryConfig.enableNamingConvention) {
      const namingResults = await this.discoverNamingConventionRelationships(
        dataSourceId,
        discoveryConfig.namingPatterns
      );
      results.push(...namingResults);
    }

    // 数据分析（值分布、唯一性等）
    if (discoveryConfig.enableDataAnalysis) {
      const dataAnalysisResults = await this.discoverDataAnalysisRelationships(
        dataSourceId,
        fileConfig
      );
      results.push(...dataAnalysisResults);
    }

    return this.filterAndRankResults(results, discoveryConfig);
  }

  /**
   * 发现跨数据源关联关系
   */
  async discoverCrossSourceRelationships(
    sourceDataSourceId: string,
    targetDataSourceId: string,
    sourceConfig: ConnectionConfig,
    targetConfig: ConnectionConfig,
    discoveryConfig: RelationshipDiscoveryConfig
  ): Promise<RelationshipDiscoveryResult[]> {
    if (!discoveryConfig.enableCrossDataSource) {
      return [];
    }

    const [sourceTables, targetTables] = await Promise.all([
      this.getDataSourceTables(sourceDataSourceId),
      this.getDataSourceTables(targetDataSourceId)
    ]);

    const results: RelationshipDiscoveryResult[] = [];

    // 基于字段名称和数据分析进行跨源匹配
    for (const sourceTable of sourceTables) {
      for (const sourceColumn of sourceTable.dataColumns) {
        for (const targetTable of targetTables) {
          for (const targetColumn of targetTable.dataColumns) {
            // 命名约定匹配
            if (discoveryConfig.enableNamingConvention) {
              const namingRelationship = await this.analyzeColumnRelationship(
                sourceColumn,
                targetColumn,
                'naming_convention'
              );
              if (namingRelationship) {
                results.push(namingRelationship);
              }
            }

            // 数据分析匹配
            if (discoveryConfig.enableDataAnalysis) {
              const dataRelationship = await this.analyzeColumnRelationshipByData(
                sourceColumn,
                targetColumn,
                sourceConfig as FileConnectionConfig,
                targetConfig as FileConnectionConfig
              );
              if (dataRelationship) {
                results.push(dataRelationship);
              }
            }
          }
        }
      }
    }

    return this.filterAndRankResults(results, discoveryConfig);
  }

  /**
   * 验证关联关系的有效性
   */
  async validateRelationship(
    fromColumnId: string,
    toColumnId: string,
    config: ConnectionConfig
  ): Promise<{ isValid: boolean; confidence: number; evidence: string[] }> {
    const [fromColumn, toColumn] = await Promise.all([
      this.prisma.dataColumn.findUnique({
        where: { id: fromColumnId },
        include: { dataTable: { include: { dataSource: true } } }
      }),
      this.prisma.dataColumn.findUnique({
        where: { id: toColumnId },
        include: { dataTable: { include: { dataSource: true } } }
      })
    ]);

    if (!fromColumn || !toColumn) {
      return { isValid: false, confidence: 0, evidence: ['列不存在'] };
    }

    const evidence: string[] = [];
    let confidence = 0;

    // 检查数据类型兼容性
    if (this.areDataTypesCompatible(fromColumn.originalDataType, toColumn.originalDataType)) {
      evidence.push('数据类型兼容');
      confidence += 0.3;
    }

    // 检查命名约定
    if (this.matchesNamingConvention(fromColumn.originalColumnName, toColumn.originalColumnName)) {
      evidence.push('符合命名约定');
      confidence += 0.4;
    }

    // 如果是同一数据源的不同表，进行数据值分析
    if (fromColumn.dataTable.dataSourceId === toColumn.dataTable.dataSourceId) {
      const dataAnalysis = await this.analyzeDataValues(fromColumn, toColumn, config as FileConnectionConfig);
      confidence += dataAnalysis.confidence;
      evidence.push(...dataAnalysis.evidence);
    }

    return {
      isValid: confidence > 0.5,
      confidence,
      evidence
    };
  }

  /**
   * 通过命名约定发现关联关系
   */
  private async discoverNamingConventionRelationships(
    dataSourceId: string,
    namingPatterns: string[]
  ): Promise<RelationshipDiscoveryResult[]> {
    const tables = await this.getDataSourceTables(dataSourceId);
    const results: RelationshipDiscoveryResult[] = [];

    // 对于文件数据源，通常只有一个表，主要查找自引用关系
    for (const table of tables) {
      const columns = table.dataColumns;
      
      for (let i = 0; i < columns.length; i++) {
        for (let j = i + 1; j < columns.length; j++) {
          const col1 = columns[i];
          const col2 = columns[j];

          if (this.matchesNamingConvention(col1.originalColumnName, col2.originalColumnName)) {
            const relationship: RelationshipDiscoveryResult = {
              fromColumnId: col1.id,
              toColumnId: col2.id,
              relationshipType: this.determineRelationshipType(col1, col2),
              discoveryMethod: 'naming_convention',
              confidence: this.calculateNamingConfidence(col1.originalColumnName, col2.originalColumnName),
              confidenceScore: this.calculateNamingConfidenceScore(col1.originalColumnName, col2.originalColumnName),
              evidence: [
                '命名约定匹配',
                `${col1.originalColumnName} -> ${col2.originalColumnName}`
              ]
            };
            results.push(relationship);
          }
        }
      }
    }

    return results;
  }

  /**
   * 通过数据分析发现关联关系
   */
  private async discoverDataAnalysisRelationships(
    dataSourceId: string,
    config: FileConnectionConfig
  ): Promise<RelationshipDiscoveryResult[]> {
    const tables = await this.getDataSourceTables(dataSourceId);
    const results: RelationshipDiscoveryResult[] = [];

    // 读取文件数据进行分析
    const fileData = await this.readFileData(config);
    if (!fileData || fileData.length === 0) {
      return results;
    }

    // 分析列之间的数据关系
    for (const table of tables) {
      const columns = table.dataColumns;
      
      for (let i = 0; i < columns.length; i++) {
        for (let j = i + 1; j < columns.length; j++) {
          const col1 = columns[i];
          const col2 = columns[j];

          const analysis = await this.analyzeColumnDataRelationship(
            col1,
            col2,
            fileData
          );

          if (analysis && analysis.confidence > 0.5) {
            const relationship: RelationshipDiscoveryResult = {
              fromColumnId: col1.id,
              toColumnId: col2.id,
              relationshipType: analysis.relationshipType,
              discoveryMethod: 'data_analysis',
              confidence: this.scoreToConfidenceLevel(analysis.confidence),
              confidenceScore: analysis.confidence,
              evidence: analysis.evidence,
              metadata: analysis.metadata
            };
            results.push(relationship);
          }
        }
      }
    }

    return results;
  }

  /**
   * 读取文件数据
   */
  private async readFileData(config: FileConnectionConfig): Promise<any[]> {
    try {
      if (this.dataSourceType === 'csv') {
        return await this.readCSVData(config);
      } else if (this.dataSourceType === 'excel') {
        return await this.readExcelData(config);
      }
      return [];
    } catch (error) {
      console.error('读取文件数据失败:', error);
      return [];
    }
  }

  /**
   * 读取CSV数据
   */
  private async readCSVData(config: FileConnectionConfig): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];
      const stream = createReadStream(config.path)
        .pipe(csv({
          separator: config.delimiter || ',',
          headers: true
        }))
        .on('data', (data: any) => results.push(data))
        .on('end', () => resolve(results.slice(0, 1000))) // 限制样本数量
        .on('error', reject);
    });
  }

  /**
   * 读取Excel数据
   */
  private async readExcelData(config: FileConnectionConfig): Promise<any[]> {
    const workbook = XLSX.readFile(config.path);
    const sheetName = config.sheetName || workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    if (!worksheet) {
      return [];
    }

    const jsonData = XLSX.utils.sheet_to_json(worksheet);
    return jsonData.slice(0, 1000); // 限制样本数量
  }

  /**
   * 分析列之间的数据关系
   */
  private async analyzeColumnDataRelationship(
    col1: any,
    col2: any,
    fileData: any[]
  ): Promise<{
    confidence: number;
    relationshipType: RelationshipType;
    evidence: string[];
    metadata: any;
  } | null> {
    const col1Name = col1.originalColumnName;
    const col2Name = col2.originalColumnName;

    // 提取列数据
    const col1Values = fileData.map(row => row[col1Name]).filter(v => v != null);
    const col2Values = fileData.map(row => row[col2Name]).filter(v => v != null);

    if (col1Values.length === 0 || col2Values.length === 0) {
      return null;
    }

    let confidence = 0;
    const evidence: string[] = [];
    const metadata: any = {};

    // 1. 唯一性分析
    const col1Unique = new Set(col1Values).size;
    const col2Unique = new Set(col2Values).size;
    const col1Ratio = col1Unique / col1Values.length;
    const col2Ratio = col2Unique / col2Values.length;

    metadata.col1Uniqueness = col1Ratio;
    metadata.col2Uniqueness = col2Ratio;

    // 如果一列高度唯一，另一列重复较多，可能是主键-外键关系
    if (col1Ratio > 0.9 && col2Ratio < 0.7) {
      confidence += 0.4;
      evidence.push(`${col1Name}具有高唯一性(${(col1Ratio * 100).toFixed(1)}%)`);
      metadata.primaryKeyCandidate = col1Name;
    } else if (col2Ratio > 0.9 && col1Ratio < 0.7) {
      confidence += 0.4;
      evidence.push(`${col2Name}具有高唯一性(${(col2Ratio * 100).toFixed(1)}%)`);
      metadata.primaryKeyCandidate = col2Name;
    }

    // 2. 值重叠分析
    const col1Set = new Set(col1Values.map(v => String(v).toLowerCase()));
    const col2Set = new Set(col2Values.map(v => String(v).toLowerCase()));
    const intersection = new Set([...col1Set].filter(x => col2Set.has(x)));
    const overlapRatio = intersection.size / Math.min(col1Set.size, col2Set.size);

    metadata.valueOverlap = overlapRatio;

    if (overlapRatio > 0.3) {
      confidence += overlapRatio * 0.5;
      evidence.push(`值重叠率: ${(overlapRatio * 100).toFixed(1)}%`);
    }

    // 3. 数据类型相似性
    const col1Type = this.inferDataType(col1Values);
    const col2Type = this.inferDataType(col2Values);

    metadata.col1InferredType = col1Type;
    metadata.col2InferredType = col2Type;

    if (col1Type === col2Type) {
      confidence += 0.2;
      evidence.push(`数据类型匹配: ${col1Type}`);
    }

    // 4. 数值范围分析（对于数值类型）
    if (col1Type === 'number' && col2Type === 'number') {
      const col1Numbers = col1Values.map(v => Number(v)).filter(n => !isNaN(n));
      const col2Numbers = col2Values.map(v => Number(v)).filter(n => !isNaN(n));

      if (col1Numbers.length > 0 && col2Numbers.length > 0) {
        const col1Range = [Math.min(...col1Numbers), Math.max(...col1Numbers)];
        const col2Range = [Math.min(...col2Numbers), Math.max(...col2Numbers)];

        // 检查范围重叠
        const rangeOverlap = Math.max(0, Math.min(col1Range[1], col2Range[1]) - Math.max(col1Range[0], col2Range[0]));
        const totalRange = Math.max(col1Range[1], col2Range[1]) - Math.min(col1Range[0], col2Range[0]);
        const rangeOverlapRatio = rangeOverlap / totalRange;

        metadata.rangeOverlap = rangeOverlapRatio;

        if (rangeOverlapRatio > 0.5) {
          confidence += 0.2;
          evidence.push(`数值范围重叠: ${(rangeOverlapRatio * 100).toFixed(1)}%`);
        }
      }
    }

    if (confidence < 0.3) {
      return null;
    }

    // 确定关系类型
    let relationshipType: RelationshipType = 'many_to_one';
    if (col1Ratio > 0.9 && col2Ratio < 0.7) {
      relationshipType = 'one_to_many';
    } else if (col2Ratio > 0.9 && col1Ratio < 0.7) {
      relationshipType = 'many_to_one';
    } else if (col1Ratio > 0.9 && col2Ratio > 0.9) {
      relationshipType = 'one_to_one';
    }

    return {
      confidence,
      relationshipType,
      evidence,
      metadata
    };
  }

  /**
   * 推断数据类型
   */
  private inferDataType(values: any[]): string {
    if (values.length === 0) return 'unknown';

    const sample = values.slice(0, 100); // 取样本
    let numberCount = 0;
    let dateCount = 0;
    let booleanCount = 0;

    for (const value of sample) {
      const str = String(value).trim();

      if (str === '' || str === 'null' || str === 'undefined') continue;

      // 检查是否为数字
      if (!isNaN(Number(str)) && isFinite(Number(str))) {
        numberCount++;
        continue;
      }

      // 检查是否为日期
      const date = new Date(str);
      if (!isNaN(date.getTime()) && str.length > 8) {
        dateCount++;
        continue;
      }

      // 检查是否为布尔值
      if (['true', 'false', '1', '0', 'yes', 'no'].includes(str.toLowerCase())) {
        booleanCount++;
        continue;
      }
    }

    const total = sample.length;
    if (numberCount / total > 0.8) return 'number';
    if (dateCount / total > 0.8) return 'date';
    if (booleanCount / total > 0.8) return 'boolean';
    return 'string';
  }

  /**
   * 分析两列的关联关系（基于数据）
   */
  private async analyzeColumnRelationshipByData(
    sourceColumn: any,
    targetColumn: any,
    sourceConfig: FileConnectionConfig,
    targetConfig: FileConnectionConfig
  ): Promise<RelationshipDiscoveryResult | null> {
    // 读取两个文件的数据
    const [sourceData, targetData] = await Promise.all([
      this.readFileData(sourceConfig),
      this.readFileData(targetConfig)
    ]);

    if (!sourceData.length || !targetData.length) {
      return null;
    }

    const sourceValues = sourceData.map(row => row[sourceColumn.originalColumnName]).filter(v => v != null);
    const targetValues = targetData.map(row => row[targetColumn.originalColumnName]).filter(v => v != null);

    if (!sourceValues.length || !targetValues.length) {
      return null;
    }

    // 计算值重叠
    const sourceSet = new Set(sourceValues.map(v => String(v).toLowerCase()));
    const targetSet = new Set(targetValues.map(v => String(v).toLowerCase()));
    const intersection = new Set([...sourceSet].filter(x => targetSet.has(x)));
    const overlapRatio = intersection.size / Math.min(sourceSet.size, targetSet.size);

    if (overlapRatio < 0.3) {
      return null;
    }

    const confidence = Math.min(0.9, overlapRatio + 0.1);

    return {
      fromColumnId: sourceColumn.id,
      toColumnId: targetColumn.id,
      relationshipType: 'many_to_one',
      discoveryMethod: 'data_analysis',
      confidence: this.scoreToConfidenceLevel(confidence),
      confidenceScore: confidence,
      evidence: [
        `跨文件值重叠率: ${(overlapRatio * 100).toFixed(1)}%`,
        `共同值数量: ${intersection.size}`
      ],
      metadata: {
        valueOverlap: overlapRatio,
        commonValues: Math.min(10, intersection.size),
        sourceFile: sourceConfig.filename,
        targetFile: targetConfig.filename
      }
    };
  }

  // 共用的辅助方法
  private async getDataSourceTables(dataSourceId: string) {
    return this.prisma.dataTable.findMany({
      where: { dataSourceId },
      include: { dataColumns: true }
    });
  }

  private async analyzeColumnRelationship(
    sourceColumn: any,
    targetColumn: any,
    method: RelationshipDiscoveryMethod
  ): Promise<RelationshipDiscoveryResult | null> {
    if (!this.areDataTypesCompatible(sourceColumn.originalDataType, targetColumn.originalDataType)) {
      return null;
    }

    let confidence = 0;
    const evidence: string[] = [];

    if (method === 'naming_convention') {
      if (this.matchesNamingConvention(sourceColumn.originalColumnName, targetColumn.originalColumnName)) {
        confidence = this.calculateNamingConfidenceScore(sourceColumn.originalColumnName, targetColumn.originalColumnName);
        evidence.push('命名约定匹配');
      }
    }

    if (confidence < 0.3) {
      return null;
    }

    return {
      fromColumnId: sourceColumn.id,
      toColumnId: targetColumn.id,
      relationshipType: this.determineRelationshipType(sourceColumn, targetColumn),
      discoveryMethod: method,
      confidence: this.scoreToConfidenceLevel(confidence),
      confidenceScore: confidence,
      evidence,
      metadata: {
        sourceTable: sourceColumn.dataTable?.originalTableName,
        targetTable: targetColumn.dataTable?.originalTableName
      }
    };
  }

  private async analyzeDataValues(
    fromColumn: any,
    toColumn: any,
    config: FileConnectionConfig
  ): Promise<{ confidence: number; evidence: string[] }> {
    try {
      const fileData = await this.readFileData(config);
      if (!fileData.length) {
        return { confidence: 0, evidence: [] };
      }

      const fromValues = fileData.map(row => row[fromColumn.originalColumnName]).filter(v => v != null);
      const toValues = fileData.map(row => row[toColumn.originalColumnName]).filter(v => v != null);

      if (!fromValues.length || !toValues.length) {
        return { confidence: 0, evidence: [] };
      }

      // 计算值重叠
      const fromSet = new Set(fromValues.map(v => String(v).toLowerCase()));
      const toSet = new Set(toValues.map(v => String(v).toLowerCase()));
      const intersection = new Set([...fromSet].filter(x => toSet.has(x)));
      const overlapRatio = intersection.size / Math.min(fromSet.size, toSet.size);

      const evidence: string[] = [];
      let confidence = 0;

      if (overlapRatio > 0.3) {
        confidence = overlapRatio * 0.5;
        evidence.push(`数据值重叠率: ${(overlapRatio * 100).toFixed(1)}%`);
      }

      return { confidence, evidence };
    } catch (error) {
      return { confidence: 0, evidence: [] };
    }
  }

  private filterAndRankResults(
    results: RelationshipDiscoveryResult[],
    config: RelationshipDiscoveryConfig
  ): RelationshipDiscoveryResult[] {
    // 去重
    const uniqueResults = this.removeDuplicateRelationships(results);

    // 按置信度过滤
    const filteredResults = uniqueResults.filter(
      r => r.confidenceScore >= config.confidenceThreshold
    );

    // 按置信度排序
    filteredResults.sort((a, b) => b.confidenceScore - a.confidenceScore);

    // 限制数量
    return filteredResults.slice(0, config.maxSuggestions);
  }

  private removeDuplicateRelationships(
    results: RelationshipDiscoveryResult[]
  ): RelationshipDiscoveryResult[] {
    const seen = new Set<string>();
    return results.filter(result => {
      const key = `${result.fromColumnId}-${result.toColumnId}`;
      const reverseKey = `${result.toColumnId}-${result.fromColumnId}`;

      if (seen.has(key) || seen.has(reverseKey)) {
        return false;
      }

      seen.add(key);
      return true;
    });
  }

  private areDataTypesCompatible(type1: string, type2: string): boolean {
    // 简化的数据类型兼容性检查
    const normalizedType1 = this.normalizeDataType(type1);
    const normalizedType2 = this.normalizeDataType(type2);

    if (normalizedType1 === normalizedType2) {
      return true;
    }

    // 兼容的数据类型组合
    const compatibleTypes = [
      ['string', 'text', 'varchar'],
      ['number', 'integer', 'decimal', 'float'],
      ['date', 'datetime', 'timestamp']
    ];

    return compatibleTypes.some(group =>
      group.includes(normalizedType1) && group.includes(normalizedType2)
    );
  }

  private normalizeDataType(dataType: string): string {
    const type = dataType.toLowerCase();

    if (['varchar', 'char', 'text', 'string'].includes(type)) return 'string';
    if (['int', 'integer', 'number', 'decimal', 'float', 'double'].includes(type)) return 'number';
    if (['date', 'datetime', 'timestamp'].includes(type)) return 'date';
    if (['boolean', 'bool'].includes(type)) return 'boolean';

    return type;
  }

  private matchesNamingConvention(name1: string, name2: string): boolean {
    const lower1 = name1.toLowerCase();
    const lower2 = name2.toLowerCase();

    // 完全匹配
    if (lower1 === lower2) return true;

    // 主键-外键模式匹配
    if ((lower1 === 'id' && lower2.endsWith('_id')) ||
        (lower1.endsWith('_id') && lower2 === 'id')) {
      return true;
    }

    // 相似度匹配
    const similarity = 1 - (this.levenshteinDistance(lower1, lower2) / Math.max(lower1.length, lower2.length));
    return similarity > 0.7;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  private calculateNamingConfidence(name1: string, name2: string): ConfidenceLevel {
    const score = this.calculateNamingConfidenceScore(name1, name2);
    if (score >= 0.8) return 'high';
    if (score >= 0.5) return 'medium';
    return 'low';
  }

  private calculateNamingConfidenceScore(name1: string, name2: string): number {
    const lower1 = name1.toLowerCase();
    const lower2 = name2.toLowerCase();

    if (lower1 === lower2) return 0.9;

    if ((lower1 === 'id' && lower2.endsWith('_id')) ||
        (lower1.endsWith('_id') && lower2 === 'id')) {
      return 0.8;
    }

    const similarity = 1 - (this.levenshteinDistance(lower1, lower2) / Math.max(lower1.length, lower2.length));
    return Math.max(0, similarity - 0.2);
  }

  private determineRelationshipType(sourceColumn: any, targetColumn: any): RelationshipType {
    // 简单的关系类型判断逻辑
    return 'many_to_one';
  }

  private scoreToConfidenceLevel(score: number): ConfidenceLevel {
    if (score >= 0.8) return 'high';
    if (score >= 0.5) return 'medium';
    return 'low';
  }
}
