import { PrismaClient } from '@prisma/client';
import {
  DataSourceType,
  RelationshipDiscoveryResult,
  RelationshipDiscoveryConfig,
  AppError,
  DatabaseError
} from '../types';
import { IRelationshipDiscoveryService } from '../interfaces/relationshipDiscovery';
import { RelationshipDiscoveryFactory } from './discovery/relationshipDiscoveryFactory';
import { decryptConnectionConfig } from '../utils/encryption';

/**
 * 关联关系发现服务
 * 统一管理所有数据源的关联关系发现
 */
export class RelationshipDiscoveryService implements IRelationshipDiscoveryService {
  protected prisma: PrismaClient;
  private discoveryFactory: RelationshipDiscoveryFactory;

  // 默认配置
  private readonly defaultConfig: RelationshipDiscoveryConfig = {
    enableForeignKeyDetection: true,
    enableNamingConvention: true,
    enableDataAnalysis: true,
    enableCrossDataSource: true,
    namingPatterns: [
      'id -> {table}_id',
      '{table}_id -> id',
      '{column}_id',
      '{table}_{column}'
    ],
    confidenceThreshold: 0.3,
    maxSuggestions: 50
  };

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.discoveryFactory = new RelationshipDiscoveryFactory(prisma);
  }

  /**
   * 为指定数据源发现关联关系
   */
  async discoverRelationships(
    dataSourceId: string,
    config?: Partial<RelationshipDiscoveryConfig>
  ): Promise<RelationshipDiscoveryResult[]> {
    try {
      const dataSource = await this.prisma.dataSource.findUnique({
        where: { id: dataSourceId }
      });

      if (!dataSource) {
        throw new AppError('数据源不存在', 404);
      }

      const fullConfig = { ...this.defaultConfig, ...config };
      const connectionConfig = decryptConnectionConfig(dataSource.connectionConfig);
      const dataSourceType = dataSource.type as DataSourceType;

      // 获取对应的发现器
      const discovery = this.discoveryFactory.createDiscovery(dataSourceType);

      // 发现内部关联关系
      const results = await discovery.discoverInternalRelationships(
        dataSourceId,
        connectionConfig,
        fullConfig
      );

      return results;
    } catch (error) {
      if (error instanceof AppError) throw error;
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new DatabaseError(`发现关联关系失败: ${errorMessage}`);
    }
  }

  /**
   * 发现跨数据源关联关系
   */
  async discoverCrossSourceRelationships(
    sourceDataSourceId: string,
    targetDataSourceId?: string,
    config?: Partial<RelationshipDiscoveryConfig>
  ): Promise<RelationshipDiscoveryResult[]> {
    try {
      const fullConfig = { ...this.defaultConfig, ...config };
      
      if (!fullConfig.enableCrossDataSource) {
        return [];
      }

      const sourceDataSource = await this.prisma.dataSource.findUnique({
        where: { id: sourceDataSourceId }
      });

      if (!sourceDataSource) {
        throw new AppError('源数据源不存在', 404);
      }

      let targetDataSources;
      if (targetDataSourceId) {
        const targetDataSource = await this.prisma.dataSource.findUnique({
          where: { id: targetDataSourceId }
        });
        if (!targetDataSource) {
          throw new AppError('目标数据源不存在', 404);
        }
        targetDataSources = [targetDataSource];
      } else {
        // 获取所有其他数据源
        targetDataSources = await this.prisma.dataSource.findMany({
          where: {
            id: { not: sourceDataSourceId }
          }
        });
      }

      const allResults: RelationshipDiscoveryResult[] = [];

      // 与每个目标数据源进行关联关系发现
      for (const targetDataSource of targetDataSources) {
        const sourceConfig = decryptConnectionConfig(sourceDataSource.connectionConfig);
        const targetConfig = decryptConnectionConfig(targetDataSource.connectionConfig);
        const sourceType = sourceDataSource.type as DataSourceType;
        const targetType = targetDataSource.type as DataSourceType;

        // 使用源数据源的发现器
        const sourceDiscovery = this.discoveryFactory.createDiscovery(sourceType);
        
        const results = await sourceDiscovery.discoverCrossSourceRelationships(
          sourceDataSourceId,
          targetDataSource.id,
          sourceConfig,
          targetConfig,
          fullConfig
        );

        allResults.push(...results);

        // 如果源和目标数据源类型不同，也尝试使用目标数据源的发现器
        if (sourceType !== targetType) {
          const targetDiscovery = this.discoveryFactory.createDiscovery(targetType);
          
          const reverseResults = await targetDiscovery.discoverCrossSourceRelationships(
            targetDataSource.id,
            sourceDataSourceId,
            targetConfig,
            sourceConfig,
            fullConfig
          );

          allResults.push(...reverseResults);
        }
      }

      // 去重和排序
      return this.deduplicateAndSort(allResults, fullConfig);
    } catch (error) {
      if (error instanceof AppError) throw error;
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new DatabaseError(`发现跨数据源关联关系失败: ${errorMessage}`);
    }
  }

  /**
   * 批量发现所有数据源的关联关系
   */
  async discoverAllRelationships(
    config?: Partial<RelationshipDiscoveryConfig>
  ): Promise<RelationshipDiscoveryResult[]> {
    try {
      const fullConfig = { ...this.defaultConfig, ...config };
      const dataSources = await this.prisma.dataSource.findMany();

      const allResults: RelationshipDiscoveryResult[] = [];

      // 1. 发现每个数据源内部的关联关系
      for (const dataSource of dataSources) {
        const internalResults = await this.discoverRelationships(dataSource.id, fullConfig);
        allResults.push(...internalResults);
      }

      // 2. 发现跨数据源关联关系
      if (fullConfig.enableCrossDataSource) {
        for (let i = 0; i < dataSources.length; i++) {
          for (let j = i + 1; j < dataSources.length; j++) {
            const crossResults = await this.discoverCrossSourceRelationships(
              dataSources[i].id,
              dataSources[j].id,
              fullConfig
            );
            allResults.push(...crossResults);
          }
        }
      }

      return this.deduplicateAndSort(allResults, fullConfig);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new DatabaseError(`批量发现关联关系失败: ${errorMessage}`);
    }
  }

  /**
   * 应用发现的关联关系到数据库
   */
  async applyDiscoveredRelationships(
    relationships: RelationshipDiscoveryResult[],
    autoApplyThreshold: number = 0.8
  ): Promise<{
    applied: number;
    skipped: number;
    failed: number;
    details: Array<{
      relationship: RelationshipDiscoveryResult;
      status: 'applied' | 'skipped' | 'failed';
      reason?: string;
    }>;
  }> {
    const results = {
      applied: 0,
      skipped: 0,
      failed: 0,
      details: [] as Array<{
        relationship: RelationshipDiscoveryResult;
        status: 'applied' | 'skipped' | 'failed';
        reason?: string;
      }>
    };

    for (const relationship of relationships) {
      try {
        // 检查是否已存在相同的关联关系
        const existingRelationship = await this.prisma.dataRelationship.findFirst({
          where: {
            OR: [
              { 
                fromColumnId: relationship.fromColumnId, 
                toColumnId: relationship.toColumnId 
              },
              { 
                fromColumnId: relationship.toColumnId, 
                toColumnId: relationship.fromColumnId 
              }
            ]
          }
        });

        if (existingRelationship) {
          results.skipped++;
          results.details.push({
            relationship,
            status: 'skipped',
            reason: '关联关系已存在'
          });
          continue;
        }

        // 根据置信度决定是否自动应用
        if (relationship.confidenceScore < autoApplyThreshold) {
          results.skipped++;
          results.details.push({
            relationship,
            status: 'skipped',
            reason: `置信度过低 (${relationship.confidenceScore.toFixed(2)} < ${autoApplyThreshold})`
          });
          continue;
        }

        // 创建关联关系
        await this.prisma.dataRelationship.create({
          data: {
            fromColumnId: relationship.fromColumnId,
            toColumnId: relationship.toColumnId,
            relationshipType: relationship.relationshipType,
            isManual: false,
            discoveryMethod: relationship.discoveryMethod,
            confidence: relationship.confidence,
            confidenceScore: relationship.confidenceScore,
            evidence: JSON.stringify(relationship.evidence),
            metadata: relationship.metadata ? JSON.stringify(relationship.metadata) : null
          }
        });

        results.applied++;
        results.details.push({
          relationship,
          status: 'applied'
        });

      } catch (error) {
        results.failed++;
        results.details.push({
          relationship,
          status: 'failed',
          reason: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return results;
  }

  /**
   * 去重和排序结果
   */
  private deduplicateAndSort(
    results: RelationshipDiscoveryResult[],
    config: RelationshipDiscoveryConfig
  ): RelationshipDiscoveryResult[] {
    // 去重
    const uniqueResults = this.removeDuplicates(results);

    // 按置信度过滤
    const filteredResults = uniqueResults.filter(
      r => r.confidenceScore >= config.confidenceThreshold
    );

    // 按置信度排序
    filteredResults.sort((a, b) => b.confidenceScore - a.confidenceScore);

    // 限制数量
    return filteredResults.slice(0, config.maxSuggestions);
  }

  /**
   * 移除重复的关联关系
   */
  private removeDuplicates(results: RelationshipDiscoveryResult[]): RelationshipDiscoveryResult[] {
    const seen = new Map<string, RelationshipDiscoveryResult>();

    for (const result of results) {
      const key1 = `${result.fromColumnId}-${result.toColumnId}`;
      const key2 = `${result.toColumnId}-${result.fromColumnId}`;

      // 如果已存在，保留置信度更高的
      if (seen.has(key1) || seen.has(key2)) {
        const existingKey = seen.has(key1) ? key1 : key2;
        const existing = seen.get(existingKey)!;
        
        if (result.confidenceScore > existing.confidenceScore) {
          seen.set(existingKey, result);
        }
      } else {
        seen.set(key1, result);
      }
    }

    return Array.from(seen.values());
  }
}
