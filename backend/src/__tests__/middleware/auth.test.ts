import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { authenticateJWT, optionalAuthenticateJWT, generateJWT, verifyJWT } from '../../middleware/auth';
import { AppError } from '../../types';

// 模拟Express请求和响应对象
const mockRequest = (headers: any = {}) => ({
  headers,
  user: undefined
} as any);

const mockResponse = () => ({
  status: jest.fn().mockReturnThis(),
  json: jest.fn().mockReturnThis()
} as any);

const mockNext = jest.fn() as jest.MockedFunction<NextFunction>;

describe('JWT认证中间件测试', () => {
  const originalEnv = process.env;
  
  beforeEach(() => {
    jest.clearAllMocks();
    // 重置环境变量
    process.env = { ...originalEnv };
    process.env.JWT_SECRET = 'test-secret-key';
    process.env.NODE_ENV = 'test';
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('authenticateJWT 中间件', () => {
    it('应该在开发模式下跳过JWT验证（DEV_MODE=true）', () => {
      process.env.DEV_MODE = 'true';
      process.env.NODE_ENV = 'development';
      
      const req = mockRequest();
      const res = mockResponse();
      
      authenticateJWT(req, res, mockNext);
      
      expect(req.user).toEqual({
        id: 'dev-user-id',
        email: '<EMAIL>',
        role: 'admin'
      });
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('应该在SKIP_JWT_AUTH=true时跳过JWT验证', () => {
      process.env.SKIP_JWT_AUTH = 'true';
      process.env.NODE_ENV = 'production';
      
      const req = mockRequest();
      const res = mockResponse();
      
      authenticateJWT(req, res, mockNext);
      
      expect(req.user).toEqual({
        id: 'dev-user-id',
        email: '<EMAIL>',
        role: 'admin'
      });
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('应该在缺少Authorization头时返回401错误', () => {
      process.env.DEV_MODE = 'false';
      process.env.SKIP_JWT_AUTH = 'false';
      
      const req = mockRequest();
      const res = mockResponse();
      
      authenticateJWT(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(expect.any(AppError));
      const error = mockNext.mock.calls[0][0] as unknown as AppError;
      expect(error.message).toBe('缺少Authorization头');
      expect(error.statusCode).toBe(401);
    });

    it('应该在Authorization头格式错误时返回401错误', () => {
      process.env.DEV_MODE = 'false';
      process.env.SKIP_JWT_AUTH = 'false';
      
      const req = mockRequest({ authorization: 'InvalidFormat token' });
      const res = mockResponse();
      
      authenticateJWT(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(expect.any(AppError));
      const error = mockNext.mock.calls[0][0] as unknown as AppError;
      expect(error.message).toBe('Authorization头格式错误，应为: Bearer <token>');
    });

    it('应该在有效JWT token时成功验证', () => {
      process.env.DEV_MODE = 'false';
      process.env.SKIP_JWT_AUTH = 'false';
      
      const payload = { id: 'user123', email: '<EMAIL>', role: 'user' };
      const token = jwt.sign(payload, 'test-secret-key');
      
      const req = mockRequest({ authorization: `Bearer ${token}` });
      const res = mockResponse();
      
      authenticateJWT(req, res, mockNext);
      
      expect(req.user).toEqual(expect.objectContaining(payload));
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('应该在无效JWT token时返回401错误', () => {
      process.env.DEV_MODE = 'false';
      process.env.SKIP_JWT_AUTH = 'false';
      
      const req = mockRequest({ authorization: 'Bearer invalid-token' });
      const res = mockResponse();
      
      authenticateJWT(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(expect.any(AppError));
      const error = mockNext.mock.calls[0][0] as unknown as AppError;
      expect(error.message).toBe('无效的JWT token');
    });

    it('应该在JWT token过期时返回401错误', () => {
      process.env.DEV_MODE = 'false';
      process.env.SKIP_JWT_AUTH = 'false';
      
      const payload = { id: 'user123', email: '<EMAIL>' };
      const expiredToken = jwt.sign(payload, 'test-secret-key', { expiresIn: '-1s' });
      
      const req = mockRequest({ authorization: `Bearer ${expiredToken}` });
      const res = mockResponse();
      
      authenticateJWT(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(expect.any(AppError));
      const error = mockNext.mock.calls[0][0] as unknown as AppError;
      expect(error.message).toBe('JWT token已过期');
    });
  });

  describe('optionalAuthenticateJWT 中间件', () => {
    it('应该在没有Authorization头时继续执行', () => {
      process.env.DEV_MODE = 'false';
      process.env.SKIP_JWT_AUTH = 'false';
      
      const req = mockRequest();
      const res = mockResponse();
      
      optionalAuthenticateJWT(req, res, mockNext);
      
      expect(req.user).toBeUndefined();
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('应该在有效token时设置用户信息', () => {
      process.env.DEV_MODE = 'false';
      process.env.SKIP_JWT_AUTH = 'false';
      
      const payload = { id: 'user123', email: '<EMAIL>', role: 'user' };
      const token = jwt.sign(payload, 'test-secret-key');
      
      const req = mockRequest({ authorization: `Bearer ${token}` });
      const res = mockResponse();
      
      optionalAuthenticateJWT(req, res, mockNext);
      
      expect(req.user).toEqual(expect.objectContaining(payload));
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('应该在无效token时继续执行而不抛出错误', () => {
      process.env.DEV_MODE = 'false';
      process.env.SKIP_JWT_AUTH = 'false';
      
      const req = mockRequest({ authorization: 'Bearer invalid-token' });
      const res = mockResponse();
      
      optionalAuthenticateJWT(req, res, mockNext);
      
      expect(req.user).toBeUndefined();
      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('JWT工具函数', () => {
    it('generateJWT应该生成有效的JWT token', () => {
      const payload = { id: 'user123', email: '<EMAIL>', role: 'user' };
      const token = generateJWT(payload);
      
      expect(typeof token).toBe('string');
      
      const decoded = jwt.verify(token, 'test-secret-key') as any;
      expect(decoded.id).toBe(payload.id);
      expect(decoded.email).toBe(payload.email);
      expect(decoded.role).toBe(payload.role);
    });

    it('verifyJWT应该验证有效的JWT token', () => {
      const payload = { id: 'user123', email: '<EMAIL>', role: 'user' };
      const token = jwt.sign(payload, 'test-secret-key');
      
      const decoded = verifyJWT(token);
      
      expect(decoded.id).toBe(payload.id);
      expect(decoded.email).toBe(payload.email);
      expect(decoded.role).toBe(payload.role);
    });

    it('verifyJWT应该在无效token时抛出错误', () => {
      expect(() => {
        verifyJWT('invalid-token');
      }).toThrow();
    });
  });
});
