# Database Configuration
# 数据库提供者类型: postgresql 或 sqlite
DATABASE_PROVIDER="postgresql"

# PostgreSQL 配置 (当 DATABASE_PROVIDER=postgresql 时使用)
DATABASE_URL="postgresql://ai_bi_user:ai_bi_password@localhost:5432/ai_bi_system"

# SQLite 配置 (当 DATABASE_PROVIDER=sqlite 时使用)
# DATABASE_URL="file:./data/ai-bi.sqlite"

# Redis
# 设置为空字符串或注释掉此行将禁用Redis缓存功能
REDIS_URL="redis://localhost:6379"
# REDIS_URL=""  # 取消注释此行以禁用Redis

# Server
PORT=3001
NODE_ENV=development

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Development Mode Settings
# 开发模式配置：设置为 'true' 时在开发环境下跳过JWT验证
DEV_MODE=false

# JWT Authentication Skip
# 强制跳过JWT验证：设置为 'true' 时在任何环境下都跳过JWT验证（优先级高于DEV_MODE）
# 警告：生产环境中请勿设置为 true
SKIP_JWT_AUTH=false

# OpenAI API
OPENAI_API_KEY=your-openai-api-key

# File Upload
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=uploads

# CORS
CORS_ORIGIN=http://localhost:3000
