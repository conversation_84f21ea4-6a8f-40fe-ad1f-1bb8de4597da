import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
  Alert,
  CircularProgress,
  LinearProgress,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  AutoAwesome as AutoAwesomeIcon,
  PlayArrow as PlayArrowIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { dataSourceApi, semanticApi } from '../services/api';
import { 
  RelationshipDiscoveryResult, 
  RelationshipDiscoveryResponse,
  RelationshipApplyResult,
  DataSourceListItem 
} from '../types';

const RelationshipReview: React.FC = () => {
  const queryClient = useQueryClient();
  const [selectedDataSource, setSelectedDataSource] = useState<string>('');
  const [discoveredRelationships, setDiscoveredRelationships] = useState<RelationshipDiscoveryResult[]>([]);
  const [selectedRelationships, setSelectedRelationships] = useState<Set<string>>(new Set());
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  const [discoveryConfig, setDiscoveryConfig] = useState({
    enableForeignKeyDetection: true,
    enableNamingConvention: true,
    enableDataAnalysis: true,
    enableCrossDataSource: true,
    confidenceThreshold: 0.3,
    maxSuggestions: 100,
    autoApply: false,
    autoApplyThreshold: 0.8
  });

  // 查询数据源列表
  const { data: dataSources } = useQuery('dataSources', dataSourceApi.getDataSources);

  // 批量发现关联关系
  const discoverAllMutation = useMutation(
    () => semanticApi.discoverAllRelationships(discoveryConfig),
    {
      onSuccess: (result: RelationshipDiscoveryResponse) => {
        setDiscoveredRelationships(result.relationships);
        setSnackbar({ 
          open: true, 
          message: `成功发现 ${result.discoveredCount} 个关联关系建议`, 
          severity: 'success' 
        });
      },
      onError: (error: Error) => {
        setSnackbar({ open: true, message: `发现关联关系失败: ${error.message}`, severity: 'error' });
      }
    }
  );

  // 发现单个数据源关联关系
  const discoverSingleMutation = useMutation(
    (dataSourceId: string) => semanticApi.discoverRelationships(dataSourceId),
    {
      onSuccess: (count: number) => {
        // 重新获取发现的关联关系
        discoverAllMutation.mutate();
        setSnackbar({ 
          open: true, 
          message: `成功发现 ${count} 个关联关系建议`, 
          severity: 'success' 
        });
      },
      onError: (error: Error) => {
        setSnackbar({ open: true, message: `发现关联关系失败: ${error.message}`, severity: 'error' });
      }
    }
  );

  // 应用选中的关联关系
  const applyRelationshipsMutation = useMutation(
    (relationships: RelationshipDiscoveryResult[]) => 
      semanticApi.applyDiscoveredRelationships(relationships, discoveryConfig.autoApplyThreshold),
    {
      onSuccess: (result: RelationshipApplyResult) => {
        setSnackbar({ 
          open: true, 
          message: `成功应用 ${result.applied} 个关联关系，跳过 ${result.skipped} 个，失败 ${result.failed} 个`, 
          severity: 'success' 
        });
        // 清空选中的关联关系并重新发现
        setSelectedRelationships(new Set());
        discoverAllMutation.mutate();
      },
      onError: (error: Error) => {
        setSnackbar({ open: true, message: `应用关联关系失败: ${error.message}`, severity: 'error' });
      }
    }
  );

  const handleDiscoverAll = () => {
    discoverAllMutation.mutate();
  };

  const handleDiscoverSingle = () => {
    if (selectedDataSource) {
      discoverSingleMutation.mutate(selectedDataSource);
    }
  };

  const handleSelectRelationship = (relationshipId: string) => {
    const newSelected = new Set(selectedRelationships);
    if (newSelected.has(relationshipId)) {
      newSelected.delete(relationshipId);
    } else {
      newSelected.add(relationshipId);
    }
    setSelectedRelationships(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedRelationships.size === discoveredRelationships.length) {
      setSelectedRelationships(new Set());
    } else {
      setSelectedRelationships(new Set(discoveredRelationships.map(r => r.id)));
    }
  };

  const handleApplySelected = () => {
    const selectedRelationshipObjects = discoveredRelationships.filter(r => 
      selectedRelationships.has(r.id)
    );
    if (selectedRelationshipObjects.length > 0) {
      applyRelationshipsMutation.mutate(selectedRelationshipObjects);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.5) return 'warning';
    return 'error';
  };

  const getConfidenceText = (confidence: number) => {
    if (confidence >= 0.8) return '高';
    if (confidence >= 0.5) return '中';
    return '低';
  };

  const connectedDataSources = dataSources?.filter(ds => ds.status === 'connected') || [];

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">关联关系审核</Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleDiscoverAll}
            disabled={discoverAllMutation.isLoading}
          >
            重新发现所有关联
          </Button>
          {selectedRelationships.size > 0 && (
            <Button
              variant="contained"
              startIcon={<PlayArrowIcon />}
              onClick={handleApplySelected}
              disabled={applyRelationshipsMutation.isLoading}
            >
              应用选中的关联关系 ({selectedRelationships.size})
            </Button>
          )}
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* 发现配置面板 */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                发现配置
              </Typography>
              
              {/* 数据源选择 */}
              <FormControl fullWidth margin="normal">
                <InputLabel>选择数据源（单独发现）</InputLabel>
                <Select
                  value={selectedDataSource}
                  onChange={(e) => setSelectedDataSource(e.target.value)}
                >
                  <MenuItem value="">
                    <em>选择数据源</em>
                  </MenuItem>
                  {connectedDataSources.map((ds) => (
                    <MenuItem key={ds.id} value={ds.id}>
                      {ds.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Button
                fullWidth
                variant="outlined"
                startIcon={<AutoAwesomeIcon />}
                onClick={handleDiscoverSingle}
                disabled={!selectedDataSource || discoverSingleMutation.isLoading}
                sx={{ mt: 1, mb: 2 }}
              >
                发现选中数据源关联
              </Button>

              {/* 配置选项 */}
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle2">高级配置</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Box display="flex" flexDirection="column" gap={2}>
                    <TextField
                      label="置信度阈值"
                      type="number"
                      value={discoveryConfig.confidenceThreshold}
                      onChange={(e) => setDiscoveryConfig({
                        ...discoveryConfig,
                        confidenceThreshold: parseFloat(e.target.value)
                      })}
                      inputProps={{ min: 0, max: 1, step: 0.1 }}
                      size="small"
                    />
                    <TextField
                      label="最大建议数量"
                      type="number"
                      value={discoveryConfig.maxSuggestions}
                      onChange={(e) => setDiscoveryConfig({
                        ...discoveryConfig,
                        maxSuggestions: parseInt(e.target.value)
                      })}
                      inputProps={{ min: 1, max: 1000 }}
                      size="small"
                    />
                  </Box>
                </AccordionDetails>
              </Accordion>
            </CardContent>
          </Card>

          {/* 统计信息 */}
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                发现统计
              </Typography>
              <Typography variant="body2" color="textSecondary">
                总发现数量: {discoveredRelationships.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                已选中: {selectedRelationships.size}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                高置信度: {discoveredRelationships.filter(r => r.confidence >= 0.8).length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                中置信度: {discoveredRelationships.filter(r => r.confidence >= 0.5 && r.confidence < 0.8).length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                低置信度: {discoveredRelationships.filter(r => r.confidence < 0.5).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* 关联关系列表 */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  发现的关联关系 ({discoveredRelationships.length})
                </Typography>
                <Button
                  size="small"
                  onClick={handleSelectAll}
                  disabled={discoveredRelationships.length === 0}
                >
                  {selectedRelationships.size === discoveredRelationships.length ? '取消全选' : '全选'}
                </Button>
              </Box>

              {discoverAllMutation.isLoading && (
                <Box mb={2}>
                  <LinearProgress />
                  <Typography variant="body2" color="textSecondary" textAlign="center" mt={1}>
                    正在发现关联关系...
                  </Typography>
                </Box>
              )}

              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox">选择</TableCell>
                      <TableCell>源表.列</TableCell>
                      <TableCell>目标表.列</TableCell>
                      <TableCell>关系类型</TableCell>
                      <TableCell>置信度</TableCell>
                      <TableCell>原因</TableCell>
                      <TableCell>状态</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {discoveredRelationships.map((relationship) => (
                      <TableRow key={relationship.id} hover>
                        <TableCell padding="checkbox">
                          <input
                            type="checkbox"
                            checked={selectedRelationships.has(relationship.id)}
                            onChange={() => handleSelectRelationship(relationship.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {relationship.fromTableName}.{relationship.fromColumnName}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {relationship.toTableName}.{relationship.toColumnName}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={relationship.relationshipType} 
                            size="small" 
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={`${(relationship.confidence * 100).toFixed(0)}% (${getConfidenceText(relationship.confidence)})`}
                            color={getConfidenceColor(relationship.confidence)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Tooltip title={relationship.reasons.join(', ')}>
                            <IconButton size="small">
                              <InfoIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={relationship.isApplied ? '已应用' : '待审核'}
                            color={relationship.isApplied ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                    {discoveredRelationships.length === 0 && !discoverAllMutation.isLoading && (
                      <TableRow>
                        <TableCell colSpan={7} align="center">
                          <Typography color="textSecondary">
                            暂无发现的关联关系，点击"重新发现所有关联"开始分析
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default RelationshipReview;
