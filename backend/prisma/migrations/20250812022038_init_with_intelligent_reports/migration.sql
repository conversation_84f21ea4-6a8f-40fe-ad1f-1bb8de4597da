-- CreateTable
CREATE TABLE "data_sources" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "connectionConfig" TEXT NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "data_tables" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "data_source_id" TEXT NOT NULL,
    "original_table_name" TEXT NOT NULL,
    "alias_name" TEXT,
    "description" TEXT,
    "is_synced" BOOLEAN NOT NULL DEFAULT false,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "data_tables_data_source_id_fkey" FOREIGN KEY ("data_source_id") REFERENCES "data_sources" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "data_columns" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "data_table_id" TEXT NOT NULL,
    "original_column_name" TEXT NOT NULL,
    "original_data_type" TEXT NOT NULL,
    "alias_name" TEXT,
    "description" TEXT,
    "is_primary_key" BOOLEAN NOT NULL DEFAULT false,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "data_columns_data_table_id_fkey" FOREIGN KEY ("data_table_id") REFERENCES "data_tables" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "data_relationships" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "from_column_id" TEXT NOT NULL,
    "to_column_id" TEXT NOT NULL,
    "relationship_type" TEXT NOT NULL,
    "is_manual" BOOLEAN NOT NULL DEFAULT false,
    "discovery_method" TEXT,
    "confidence" TEXT,
    "confidence_score" REAL,
    "evidence" TEXT NOT NULL,
    "metadata" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "data_relationships_from_column_id_fkey" FOREIGN KEY ("from_column_id") REFERENCES "data_columns" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "data_relationships_to_column_id_fkey" FOREIGN KEY ("to_column_id") REFERENCES "data_columns" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "chat_history" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "session_id" TEXT NOT NULL,
    "user_query" TEXT NOT NULL,
    "generated_sql" TEXT,
    "query_result_data" TEXT,
    "natural_language_response" TEXT,
    "visualization_spec" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "user_profiles" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "user_id" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "preferences" TEXT NOT NULL,
    "query_history" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "report_templates" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "config" TEXT NOT NULL,
    "user_role" TEXT NOT NULL,
    "domain" TEXT NOT NULL,
    "is_public" BOOLEAN NOT NULL DEFAULT false,
    "created_by" TEXT NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "report_instances" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "template_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "metadata" TEXT,
    "status" TEXT NOT NULL,
    "generated_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expires_at" DATETIME,
    CONSTRAINT "report_instances_template_id_fkey" FOREIGN KEY ("template_id") REFERENCES "report_templates" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "report_instances_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user_profiles" ("user_id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "scheduled_tasks" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "template_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "schedule_type" TEXT NOT NULL,
    "cron_expression" TEXT NOT NULL,
    "config" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "last_run" DATETIME,
    "next_run" DATETIME,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "scheduled_tasks_template_id_fkey" FOREIGN KEY ("template_id") REFERENCES "report_templates" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "scheduled_tasks_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user_profiles" ("user_id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "data_insights" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "data_source_id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "confidence" REAL NOT NULL,
    "severity" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "metadata" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "data_insights_data_source_id_fkey" FOREIGN KEY ("data_source_id") REFERENCES "data_sources" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "user_profiles_user_id_key" ON "user_profiles"("user_id");
