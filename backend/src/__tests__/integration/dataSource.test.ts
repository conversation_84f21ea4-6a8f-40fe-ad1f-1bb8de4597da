import request from 'supertest';
import express from 'express';
import { PrismaClient } from '@prisma/client';

// 设置为开发模式以跳过JWT认证（必须在导入路由之前）
process.env.NODE_ENV = 'development';
process.env.DEV_MODE = 'true';

import dataSourceRoutes from '../../routes/dataSourceRoutes';
import { errorHandler } from '../../middleware/errorHandler';

// 创建测试应用
const app = express();
app.use(express.json());
app.use('/api/data-sources', dataSourceRoutes);
app.use(errorHandler);

// 全局错误处理
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  res.status(err.status || 500).json({
    success: false,
    error: err.message || 'Internal Server Error'
  });
});

// Mock PrismaClient
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    dataSource: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    },
    dataRelationship: {
      findMany: jest.fn().mockResolvedValue([])
    },
    $disconnect: jest.fn().mockResolvedValue(undefined)
  }))
}));

// Mock the prisma instance from lib/prisma
jest.mock('../../lib/prisma', () => ({
  prisma: {
    dataSource: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    },
    dataRelationship: {
      findMany: jest.fn().mockResolvedValue([])
    },
    $disconnect: jest.fn().mockResolvedValue(undefined)
  }
}));

describe('Data Source API Integration Tests', () => {
  let prisma: PrismaClient;
  let mockPrismaInstance: any;

  beforeEach(() => {
    prisma = new PrismaClient();
    // Get reference to the mocked lib/prisma instance
    const { prisma: libPrisma } = require('../../lib/prisma');
    mockPrismaInstance = libPrisma;
    jest.clearAllMocks();

    // Reset all mocks to default behavior
    mockPrismaInstance.dataSource.findMany.mockResolvedValue([
      {
        id: '1',
        name: 'Test PostgreSQL',
        type: 'postgresql',
        connectionConfig: 'encrypted-config',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);

    mockPrismaInstance.dataSource.findUnique.mockResolvedValue({
      id: '1',
      name: 'Test DB',
      type: 'postgresql',
      connectionConfig: 'encrypted-config',
      createdAt: new Date(),
      updatedAt: new Date(),
      dataTables: [
        {
          id: 'table-1',
          originalTableName: 'users',
          aliasName: '用户表',
          dataColumns: []
        }
      ]
    });

    mockPrismaInstance.dataSource.create.mockResolvedValue({
      id: 'new-id',
      name: 'Test Database',
      type: 'postgresql',
      connectionConfig: 'encrypted-config',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    mockPrismaInstance.dataSource.delete.mockResolvedValue({
      id: '1',
      name: 'Test Database',
      type: 'postgresql',
      connectionConfig: 'encrypted-config',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Mock DataSourceService methods
    const { DataSourceService } = require('../../services/dataSourceService');
    jest.spyOn(DataSourceService.prototype, 'testConnection').mockResolvedValue(true);
    jest.spyOn(DataSourceService.prototype, 'syncMetadata').mockResolvedValue(undefined);
  });

  describe('GET /api/data-sources', () => {
    it('should return list of data sources', async () => {
      const mockDataSources = [
        {
          id: '1',
          name: 'Test PostgreSQL',
          type: 'postgresql',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      (prisma.dataSource.findMany as jest.Mock).mockResolvedValue(mockDataSources);

      const response = await request(app)
        .get('/api/data-sources')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.arrayContaining([
          expect.objectContaining({
            id: '1',
            name: 'Test PostgreSQL',
            type: 'postgresql',
          }),
        ]),
      });
    });

    it('should handle database errors', async () => {
      mockPrismaInstance.dataSource.findMany.mockRejectedValue(new Error('Database connection failed'));

      const response = await request(app)
        .get('/api/data-sources')
        .expect(500);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('获取数据源列表失败'),
      });
    });
  });

  describe('POST /api/data-sources', () => {
    it('should create a new data source', async () => {
      const newDataSource = {
        name: 'New PostgreSQL DB',
        type: 'postgresql',
        connectionConfig: {
          host: 'localhost',
          port: 5432,
          database: 'test_db',
          username: 'test_user',
          password: 'test_password',
        },
      };

      const mockCreatedDataSource = {
        id: 'new-id',
        ...newDataSource,
        connectionConfig: 'encrypted-config',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      (prisma.dataSource.create as jest.Mock).mockResolvedValue(mockCreatedDataSource);

      const response = await request(app)
        .post('/api/data-sources')
        .send(newDataSource)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        data: { id: 'new-id' },
        message: '数据源创建成功',
      });
    });

    it('should validate required fields', async () => {
      const invalidDataSource = {
        name: '', // 空名称
        type: 'postgresql',
        connectionConfig: {},
      };

      const response = await request(app)
        .post('/api/data-sources')
        .send(invalidDataSource)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('数据源名称不能为空'),
      });
    });

    it('should validate data source type', async () => {
      const invalidDataSource = {
        name: 'Test DB',
        type: 'invalid-type',
        connectionConfig: {},
      };

      const response = await request(app)
        .post('/api/data-sources')
        .send(invalidDataSource)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('数据源类型必须是'),
      });
    });
  });

  describe('GET /api/data-sources/:id', () => {
    it('should return data source details', async () => {
      const mockDataSource = {
        id: '1',
        name: 'Test DB',
        type: 'postgresql',
        dataTables: [
          {
            id: 'table-1',
            originalTableName: 'users',
            aliasName: '用户表',
            dataColumns: [
              {
                id: 'column-1',
                originalColumnName: 'id',
                originalDataType: 'integer',
                aliasName: '用户ID',
                isPrimaryKey: true,
              },
            ],
          },
        ],
      };

      (prisma.dataSource.findUnique as jest.Mock).mockResolvedValue(mockDataSource);
      (prisma.dataRelationship.findMany as jest.Mock).mockResolvedValue([]);

      const response = await request(app)
        .get('/api/data-sources/1')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          id: '1',
          name: 'Test DB',
          tables: expect.arrayContaining([
            expect.objectContaining({
              id: 'table-1',
              originalName: 'users',
              aliasName: '用户表',
            }),
          ]),
        }),
      });
    });

    it('should return 404 for non-existent data source', async () => {
      mockPrismaInstance.dataSource.findUnique.mockResolvedValue(null);

      const response = await request(app)
        .get('/api/data-sources/non-existent')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: '数据源不存在',
      });
    });
  });

  describe('DELETE /api/data-sources/:id', () => {
    it('should delete existing data source', async () => {
      const mockDataSource = {
        id: '1',
        name: 'Test DB',
        type: 'postgresql',
      };

      (prisma.dataSource.findUnique as jest.Mock).mockResolvedValue(mockDataSource);
      (prisma.dataSource.delete as jest.Mock).mockResolvedValue(mockDataSource);

      const response = await request(app)
        .delete('/api/data-sources/1')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: '数据源删除成功',
      });
    });

    it('should return 404 for non-existent data source', async () => {
      mockPrismaInstance.dataSource.findUnique.mockResolvedValue(null);

      const response = await request(app)
        .delete('/api/data-sources/non-existent')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: '数据源不存在',
      });
    });
  });

  describe('POST /api/data-sources/test-connection', () => {
    it('should test connection successfully', async () => {
      const connectionConfig = {
        host: 'localhost',
        port: 5432,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password',
      };

      // 这里需要mock DataSourceService的testConnection方法
      // 由于我们在集成测试中，可能需要更复杂的mock设置

      const response = await request(app)
        .post('/api/data-sources/test-connection')
        .send({
          type: 'postgresql',
          connectionConfig,
        })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: { connected: expect.any(Boolean) },
      });
    });

    it('should handle connection test failure', async () => {
      // Mock testConnection to throw an error for this test
      const { DataSourceService } = require('../../services/dataSourceService');
      jest.spyOn(DataSourceService.prototype, 'testConnection').mockRejectedValue(new Error('Connection failed'));

      const connectionConfig = {
        host: 'invalid-host',
        port: 5432,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password',
      };

      const response = await request(app)
        .post('/api/data-sources/test-connection')
        .send({
          type: 'postgresql',
          connectionConfig,
        })
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('连接测试失败'),
      });
    });
  });
});
