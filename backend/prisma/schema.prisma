// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// SQLite 数据库配置
datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 数据源表
model DataSource {
  id               String   @id @default(cuid()) // 使用 cuid() 替代 uuid() 以兼容 SQLite
  name             String   // 移除 PostgreSQL 特定的 @db 属性以兼容 SQLite
  type             String   // postgresql, mysql, csv, excel, sqlite
  connectionConfig String   // 加密后的连接配置信息 (JSON 格式)
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // 关联关系
  dataTables   DataTable[]
  dataInsights DataInsight[]

  @@map("data_sources")
}

// 数据表元数据表
model DataTable {
  id                String   @id @default(cuid())
  dataSourceId      String   @map("data_source_id")
  originalTableName String   @map("original_table_name")
  aliasName         String?  @map("alias_name")
  description       String?
  isSynced          Boolean  @default(false) @map("is_synced")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // 关联关系
  dataSource  DataSource   @relation(fields: [dataSourceId], references: [id], onDelete: Cascade)
  dataColumns DataColumn[]

  @@map("data_tables")
}

// 数据列元数据表
model DataColumn {
  id                 String   @id @default(cuid())
  dataTableId        String   @map("data_table_id")
  originalColumnName String   @map("original_column_name")
  originalDataType   String   @map("original_data_type")
  aliasName          String?  @map("alias_name")
  description        String?
  isPrimaryKey       Boolean  @default(false) @map("is_primary_key")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  // 关联关系
  dataTable DataTable @relation(fields: [dataTableId], references: [id], onDelete: Cascade)

  // 作为关联关系的起始列
  fromRelationships DataRelationship[] @relation("FromColumn")
  // 作为关联关系的目标列
  toRelationships   DataRelationship[] @relation("ToColumn")

  @@map("data_columns")
}

// 数据关联关系表
model DataRelationship {
  id               String   @id @default(cuid())
  fromColumnId     String   @map("from_column_id")
  toColumnId       String   @map("to_column_id")
  relationshipType String   @map("relationship_type") // one_to_one, one_to_many, many_to_one
  isManual         Boolean  @default(false) @map("is_manual") // 是否为用户手动创建

  // 置信度相关字段
  discoveryMethod  String?  @map("discovery_method") // foreign_key, naming_convention, data_analysis, manual
  confidence       String?  @map("confidence") // high, medium, low
  confidenceScore  Float?   @map("confidence_score") // 0-1之间的数值
  evidence         String   @map("evidence") // 支持该关联关系的证据，SQLite 不支持数组，使用 JSON 字符串存储
  metadata         String?  @map("metadata") // 额外的元数据信息，SQLite 不支持 Json 类型，使用字符串存储

  createdAt        DateTime @default(now()) @map("created_at")

  // 关联关系
  fromColumn DataColumn @relation("FromColumn", fields: [fromColumnId], references: [id], onDelete: Cascade)
  toColumn   DataColumn @relation("ToColumn", fields: [toColumnId], references: [id], onDelete: Cascade)

  @@map("data_relationships")
}

// 对话历史记录表 (用于NLQ自然语言查询)
model ChatHistory {
  id                       String   @id @default(cuid())
  sessionId                String   @map("session_id")
  userQuery                String   @map("user_query")
  generatedSql             String?  @map("generated_sql")
  queryResultData          String?  @map("query_result_data") // SQLite 不支持 Json 类型，使用字符串存储
  naturalLanguageResponse  String?  @map("natural_language_response")
  visualizationSpec        String?  @map("visualization_spec") // SQLite 不支持 Json 类型，使用字符串存储
  createdAt                DateTime @default(now()) @map("created_at")

  @@map("chat_history")
}

// LLM对话会话表 (用于通用LLM聊天)
model LLMConversation {
  id        String   @id @default(cuid())
  title     String?  // 对话标题，可以根据首条消息自动生成
  userId    String?  @map("user_id") // 用户ID，可选
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  messages LLMMessage[]

  @@map("llm_conversations")
}

// LLM消息表 (支持消息线程和分支对话)
model LLMMessage {
  id             String          @id @default(cuid())
  conversationId String          @map("conversation_id")
  role           String          // user, assistant, system
  content        String          // 消息内容
  model          String?         // 使用的模型名称 (如: deepseek, gpt-4, etc.)
  parentId       String?         @map("parent_id") // 父消息ID，支持消息线程
  requestId      String?         @map("request_id") // 请求ID，用于追踪

  // 使用量统计
  promptTokens     Int?    @map("prompt_tokens")
  completionTokens Int?    @map("completion_tokens")
  totalTokens      Int?    @map("total_tokens")

  // 元数据和选项
  finishReason     String? @map("finish_reason") // stop, length, content_filter, etc.
  deepThinking     Boolean @default(false) @map("deep_thinking") // 是否启用深度思考
  onlineSearch     Boolean @default(false) @map("online_search") // 是否启用在线搜索
  metadata         String? // 额外元数据 (JSON格式)

  createdAt        DateTime @default(now()) @map("created_at")

  // 关联关系
  conversation LLMConversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  parent       LLMMessage?     @relation("MessageThread", fields: [parentId], references: [id])
  children     LLMMessage[]    @relation("MessageThread")

  @@map("llm_messages")
}

// 用户配置表 - 用于智能报表个性化
model UserProfile {
  id            String   @id @default(cuid())
  userId        String   @unique @map("user_id") // 用户唯一标识
  role          String   // analyst, manager, executive - 用户角色
  preferences   String   @map("preferences") // 用户偏好设置 (JSON格式)
  queryHistory  String?  @map("query_history") // 查询历史模式 (JSON格式)
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关联关系
  reportInstances ReportInstance[]
  scheduledTasks  ScheduledTask[]

  @@map("user_profiles")
}

// 报表模板表
model ReportTemplate {
  id          String   @id @default(cuid())
  name        String   // 模板名称
  description String?  // 模板描述
  config      String   @map("config") // 模板配置 (JSON格式)
  userRole    String   @map("user_role") // 适用的用户角色
  domain      String   // 业务领域
  isPublic    Boolean  @default(false) @map("is_public") // 是否为公共模板
  createdBy   String   @map("created_by") // 创建者
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  reportInstances ReportInstance[]
  scheduledTasks  ScheduledTask[]

  @@map("report_templates")
}

// 报表实例表
model ReportInstance {
  id          String   @id @default(cuid())
  templateId  String   @map("template_id")
  userId      String   @map("user_id")
  title       String   // 报表标题
  content     String   @map("content") // 报表内容 (JSON格式)
  metadata    String?  @map("metadata") // 元数据信息 (JSON格式)
  status      String   // generating, completed, failed, expired
  generatedAt DateTime @default(now()) @map("generated_at")
  expiresAt   DateTime? @map("expires_at") // 过期时间

  // 关联关系
  template    ReportTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  userProfile UserProfile    @relation(fields: [userId], references: [userId], onDelete: Cascade)

  @@map("report_instances")
}

// 定时任务表
model ScheduledTask {
  id             String   @id @default(cuid())
  templateId     String   @map("template_id")
  userId         String   @map("user_id")
  scheduleType   String   @map("schedule_type") // daily, weekly, monthly
  cronExpression String   @map("cron_expression") // Cron表达式
  config         String   @map("config") // 任务配置 (JSON格式)
  isActive       Boolean  @default(true) @map("is_active") // 是否激活
  lastRun        DateTime? @map("last_run") // 上次运行时间
  nextRun        DateTime? @map("next_run") // 下次运行时间
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // 关联关系
  template    ReportTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  userProfile UserProfile    @relation(fields: [userId], references: [userId], onDelete: Cascade)

  @@map("scheduled_tasks")
}

// 数据洞察表 - 用于智能报表内容生成
model DataInsight {
  id           String   @id @default(cuid())
  dataSourceId String   @map("data_source_id")
  type         String   // anomaly, trend, opportunity, risk
  title        String   // 洞察标题
  description  String   // 洞察描述
  confidence   Float    // 置信度 (0-1)
  severity     String   // low, medium, high, critical
  status       String   // new, acknowledged, resolved
  metadata     String?  @map("metadata") // 额外元数据 (JSON格式)
  createdAt    DateTime @default(now()) @map("created_at")

  // 关联关系
  dataSource DataSource @relation(fields: [dataSourceId], references: [id], onDelete: Cascade)

  @@map("data_insights")
}
