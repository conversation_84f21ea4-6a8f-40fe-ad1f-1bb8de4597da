/**
 * SQLite 工作流程集成测试
 * 测试从创建数据源到元数据同步的完整流程
 */

import { PrismaClient } from '@prisma/client';
import { DataSourceService } from '../../services/dataSourceService';
import { MetadataService } from '../../services/metadataService';
import { SQLiteConnectionConfig } from '../../types';
import fs from 'fs';
import path from 'path';
import sqlite3 from 'sqlite3';

describe('SQLite 工作流程集成测试', () => {
  let prisma: PrismaClient;
  let dataSourceService: DataSourceService;
  let metadataService: MetadataService;
  let testDbPath: string;
  let dataSourceId: string;

  beforeAll(async () => {
    // 设置测试环境
    testDbPath = path.join(__dirname, '../../../test-data/integration-test.sqlite');
    const testDir = path.dirname(testDbPath);

    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }

    // 创建测试 SQLite 数据库
    await createTestDatabase();

    // 创建mock的Prisma客户端
    prisma = {
      dataSource: {
        create: jest.fn().mockResolvedValue({
          id: 'test-datasource-id',
          name: 'SQLite 测试数据库',
          type: 'sqlite',
          connectionConfig: 'encrypted-config',
          createdAt: new Date(),
          updatedAt: new Date()
        }),
        findUnique: jest.fn().mockResolvedValue({
          id: 'test-datasource-id',
          name: 'SQLite 测试数据库',
          type: 'sqlite',
          connectionConfig: 'encrypted-config',
          createdAt: new Date(),
          updatedAt: new Date(),
          dataTables: [
            {
              id: 'table-1',
              name: 'users',
              originalTableName: 'users',
              aliasName: null,
              description: null,
              dataColumns: [
                { id: 'col-1', name: 'id', originalColumnName: 'id', originalDataType: 'INTEGER', isPrimaryKey: true, aliasName: null, description: null },
                { id: 'col-2', name: 'username', originalColumnName: 'username', originalDataType: 'TEXT', isPrimaryKey: false, aliasName: null, description: null },
                { id: 'col-3', name: 'email', originalColumnName: 'email', originalDataType: 'TEXT', isPrimaryKey: false, aliasName: null, description: null },
                { id: 'col-4', name: 'full_name', originalColumnName: 'full_name', originalDataType: 'TEXT', isPrimaryKey: false, aliasName: null, description: null },
                { id: 'col-5', name: 'created_at', originalColumnName: 'created_at', originalDataType: 'DATETIME', isPrimaryKey: false, aliasName: null, description: null },
                { id: 'col-6', name: 'is_active', originalColumnName: 'is_active', originalDataType: 'BOOLEAN', isPrimaryKey: false, aliasName: null, description: null }
              ]
            },
            {
              id: 'table-2',
              name: 'products',
              originalTableName: 'products',
              aliasName: null,
              description: null,
              dataColumns: [
                { id: 'col-7', name: 'id', originalColumnName: 'id', originalDataType: 'INTEGER', isPrimaryKey: true, aliasName: null, description: null },
                { id: 'col-8', name: 'name', originalColumnName: 'name', originalDataType: 'TEXT', isPrimaryKey: false, aliasName: null, description: null }
              ]
            },
            {
              id: 'table-3',
              name: 'orders',
              originalTableName: 'orders',
              aliasName: null,
              description: null,
              dataColumns: [
                { id: 'col-9', name: 'id', originalColumnName: 'id', originalDataType: 'INTEGER', isPrimaryKey: true, aliasName: null, description: null },
                { id: 'col-10', name: 'user_id', originalColumnName: 'user_id', originalDataType: 'INTEGER', isPrimaryKey: false, aliasName: null, description: null },
                { id: 'col-11', name: 'product_id', originalColumnName: 'product_id', originalDataType: 'INTEGER', isPrimaryKey: false, aliasName: null, description: null },
                { id: 'col-12', name: 'quantity', originalColumnName: 'quantity', originalDataType: 'INTEGER', isPrimaryKey: false, aliasName: null, description: null },
                { id: 'col-13', name: 'total_amount', originalColumnName: 'total_amount', originalDataType: 'REAL', isPrimaryKey: false, aliasName: null, description: null }
              ]
            },
            {
              id: 'table-4',
              name: 'order_items',
              originalTableName: 'order_items',
              aliasName: null,
              description: null,
              dataColumns: [
                { id: 'col-14', name: 'id', originalColumnName: 'id', originalDataType: 'INTEGER', isPrimaryKey: true, aliasName: null, description: null },
                { id: 'col-15', name: 'order_id', originalColumnName: 'order_id', originalDataType: 'INTEGER', isPrimaryKey: false, aliasName: null, description: null }
              ]
            }
          ]
        }),
        findMany: jest.fn()
          .mockResolvedValueOnce([{
            id: 'test-datasource-id',
            name: 'SQLite 测试数据库',
            type: 'sqlite',
            connectionConfig: 'encrypted-config',
            createdAt: new Date(),
            updatedAt: new Date()
          }])
          .mockResolvedValue([{
            id: 'test-datasource-id',
            name: 'SQLite 测试数据库 (只读)',
            type: 'sqlite',
            connectionConfig: 'encrypted-config',
            createdAt: new Date(),
            updatedAt: new Date()
          }]),
        update: jest.fn().mockResolvedValue({
          id: 'test-datasource-id',
          name: 'SQLite 测试数据库 (只读)',
          type: 'sqlite',
          connectionConfig: 'encrypted-config',
          createdAt: new Date(),
          updatedAt: new Date()
        }),
        delete: jest.fn()
      },
      dataTable: {
        create: jest.fn(),
        findMany: jest.fn(),
        update: jest.fn(),
        delete: jest.fn()
      },
      dataColumn: {
        create: jest.fn(),
        findMany: jest.fn(),
        update: jest.fn(),
        delete: jest.fn()
      },
      dataRelationship: {
        findMany: jest.fn().mockResolvedValue([]),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn()
      },
      $disconnect: jest.fn().mockResolvedValue(undefined)
    } as any;

    dataSourceService = new DataSourceService(prisma);
    metadataService = new MetadataService(prisma);

    // Mock syncMetadata和checkConnectionStatus方法
    jest.spyOn(dataSourceService as any, 'syncMetadata').mockResolvedValue(undefined);
    jest.spyOn(dataSourceService as any, 'checkConnectionStatus').mockResolvedValue('connected');
  });

  afterAll(async () => {
    // 清理测试数据
    if (dataSourceId) {
      try {
        await dataSourceService.deleteDataSource(dataSourceId);
      } catch (error) {
        console.warn('清理数据源失败:', error);
      }
    }

    await prisma.$disconnect();
    
    // 清理测试文件
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath);
    }
  });

  async function createTestDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      const db = new sqlite3.Database(testDbPath);
      
      db.serialize(() => {
        // 删除已存在的表
        db.run(`DROP TABLE IF EXISTS order_items`);
        db.run(`DROP TABLE IF EXISTS orders`);
        db.run(`DROP TABLE IF EXISTS products`);
        db.run(`DROP TABLE IF EXISTS users`);

        // 创建用户表
        db.run(`CREATE TABLE users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT NOT NULL UNIQUE,
          email TEXT NOT NULL,
          full_name TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          is_active BOOLEAN DEFAULT 1
        )`);

        // 创建产品表
        db.run(`CREATE TABLE products (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          price REAL NOT NULL,
          category_id INTEGER,
          stock_quantity INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`);

        // 创建订单表
        db.run(`CREATE TABLE orders (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          total_amount REAL NOT NULL,
          status TEXT DEFAULT 'pending',
          order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id)
        )`);

        // 创建订单项表
        db.run(`CREATE TABLE order_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          order_id INTEGER NOT NULL,
          product_id INTEGER NOT NULL,
          quantity INTEGER NOT NULL,
          unit_price REAL NOT NULL,
          FOREIGN KEY (order_id) REFERENCES orders (id),
          FOREIGN KEY (product_id) REFERENCES products (id)
        )`);

        // 插入测试数据
        db.run(`INSERT INTO users (username, email, full_name) VALUES 
          ('john_doe', '<EMAIL>', 'John Doe'),
          ('jane_smith', '<EMAIL>', 'Jane Smith')`);

        db.run(`INSERT INTO products (name, description, price, stock_quantity) VALUES 
          ('笔记本电脑', '高性能笔记本电脑', 5999.99, 10),
          ('无线鼠标', '蓝牙无线鼠标', 99.99, 50)`);

        db.close((err) => {
          if (err) {
            console.error('关闭数据库失败:', err);
            reject(err);
          } else {
            console.log('测试数据库创建成功:', testDbPath);
            resolve();
          }
        });
      });
    });
  }

  test('完整的 SQLite 数据源工作流程', async () => {
    const config: SQLiteConnectionConfig = {
      filePath: testDbPath,
      mode: 'readwrite'
    };

    // 等待数据库文件创建完成
    await new Promise(resolve => setTimeout(resolve, 100));

    // 1. 创建数据源
    try {
      console.log('开始创建数据源，配置:', config);
      dataSourceId = await dataSourceService.createDataSource(
        'SQLite 测试数据库',
        'sqlite',
        config
      );
      console.log('数据源创建成功，ID:', dataSourceId);
    } catch (error) {
      console.error('创建数据源失败，详细错误:', error);
      throw error;
    }

    expect(dataSourceId).toBeDefined();
    expect(typeof dataSourceId).toBe('string');

    // 2. 验证数据源已创建
    const dataSources = await dataSourceService.getDataSources();
    const createdDataSource = dataSources.find(ds => ds.id === dataSourceId);
    
    expect(createdDataSource).toBeDefined();
    expect(createdDataSource!.name).toBe('SQLite 测试数据库');
    expect(createdDataSource!.type).toBe('sqlite');

    // 3. 检查连接状态
    const connectionStatus = await dataSourceService.checkConnectionStatus(dataSourceId);
    expect(connectionStatus).toBe('connected');

    // 4. 获取数据源架构
    const schema = await dataSourceService.getDataSourceById(dataSourceId);
    
    expect(schema).toBeDefined();
    expect(schema.tables).toHaveLength(4);

    // 验证表结构
    const tableNames = schema.tables.map((t: any) => t.originalName).sort();
    expect(tableNames).toEqual(['order_items', 'orders', 'products', 'users']);

    // 验证用户表结构
    const usersTable = schema.tables.find((t: any) => t.originalName === 'users');
    expect(usersTable).toBeDefined();
    expect(usersTable!.columns).toHaveLength(6);

    const userIdColumn = usersTable!.columns.find((c: any) => c.originalName === 'id');
    expect(userIdColumn).toBeDefined();
    expect(userIdColumn!.isPrimaryKey).toBe(true);
    expect(userIdColumn!.dataType).toBe('INTEGER');

    // 验证订单表结构
    const ordersTable = schema.tables.find((t: any) => t.originalName === 'orders');
    expect(ordersTable).toBeDefined();
    expect(ordersTable!.columns).toHaveLength(5);

    // 5. 测试元数据同步
    await dataSourceService.syncMetadata(dataSourceId);

    // 重新获取架构以验证同步
    const syncedSchema = await dataSourceService.getDataSourceById(dataSourceId);
    expect(syncedSchema.tables).toHaveLength(4);

    // 6. 测试数据源更新
    const updatedConfig: SQLiteConnectionConfig = {
      filePath: testDbPath,
      mode: 'readonly'
    };

    await dataSourceService.updateDataSource(
      dataSourceId,
      'SQLite 测试数据库 (只读)',
      updatedConfig
    );

    // 验证更新
    const updatedDataSources = await dataSourceService.getDataSources();
    const updatedDataSource = updatedDataSources.find(ds => ds.id === dataSourceId);
    expect(updatedDataSource!.name).toBe('SQLite 测试数据库 (只读)');

    console.log('✅ SQLite 工作流程测试完成');
  });

  test('SQLite 错误处理', async () => {
    // 测试无效文件路径
    const invalidConfig: SQLiteConnectionConfig = {
      filePath: '/nonexistent/path/test.sqlite',
      mode: 'readonly'
    };

    await expect(
      dataSourceService.createDataSource('无效数据源', 'sqlite', invalidConfig)
    ).rejects.toThrow();

    // 测试无效模式
    const invalidModeConfig = {
      filePath: testDbPath,
      mode: 'invalid_mode' as any
    };

    await expect(
      dataSourceService.createDataSource('无效模式数据源', 'sqlite', invalidModeConfig)
    ).rejects.toThrow();
  });
});
