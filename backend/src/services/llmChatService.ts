import { PrismaClient } from '@prisma/client';
import { 
  LLMChatRequest, 
  LLMChatResponse, 
  LLMConversation, 
  LLMMessageRecord,
  LLMClientConfig,
  LLMServiceError,
  LLMModelType
} from '../types';
import { EnhancedLLMClient } from './llm/enhancedLLMClient';

/**
 * LLM聊天服务
 * 负责管理LLM对话、消息处理、上下文维护等核心功能
 */
export class LLMChatService {
  private prisma: PrismaClient;
  private llmClients: Map<string, EnhancedLLMClient> = new Map();

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.initializeLLMClients();
  }

  /**
   * 初始化LLM客户端
   */
  private initializeLLMClients(): void {
    // 初始化支持的LLM客户端
    const configs = this.getLLMConfigs();
    
    configs.forEach(config => {
      const client = new EnhancedLLMClient(config);
      this.llmClients.set(config.model, client);
    });

    console.log(`🤖 已初始化 ${this.llmClients.size} 个LLM客户端`);
  }

  /**
   * 获取LLM配置
   */
  private getLLMConfigs(): LLMClientConfig[] {
    return [
      {
        provider: 'deepseek',
        model: 'deepseek',
        temperature: 0.7,
        maxTokens: 2000,
        timeout: 30000
      },
      {
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 2000,
        timeout: 30000
      },
      {
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 2000,
        timeout: 30000
      },
      {
        provider: 'anthropic',
        model: 'claude-3',
        temperature: 0.7,
        maxTokens: 2000,
        timeout: 30000
      }
    ];
  }

  /**
   * 处理聊天请求
   */
  async processChat(request: LLMChatRequest): Promise<LLMChatResponse> {
    try {
      console.log(`💬 处理聊天请求，模型: ${request.model}，消息数: ${request.messages.length}`);

      // 1. 验证请求
      this.validateChatRequest(request);

      // 2. 获取或创建对话
      const conversation = await this.getOrCreateConversation(request.conversation_id);

      // 3. 保存用户消息
      const userMessage = await this.saveUserMessage(conversation.id, request);

      // 4. 获取LLM客户端
      const llmClient = this.getLLMClient(request.model);

      // 5. 准备上下文消息
      const contextMessages = await this.prepareContextMessages(conversation.id, request);

      // 6. 调用LLM API
      const llmResponse = await llmClient.chat({
        ...request,
        messages: contextMessages,
        conversation_id: conversation.id
      });

      // 7. 保存助手回复
      const assistantMessage = await this.saveAssistantMessage(
        conversation.id,
        llmResponse,
        userMessage.id,
        request
      );

      // 8. 更新对话标题（如果是第一条消息）
      await this.updateConversationTitle(conversation, request.messages[0]?.content);

      console.log(`✅ 聊天请求处理完成，消息ID: ${assistantMessage.id}`);

      return {
        ...llmResponse,
        data: {
          ...llmResponse.data,
          conversation_id: conversation.id,
          parent_message_id: userMessage.id
        }
      };

    } catch (error) {
      console.error('❌ 聊天请求处理失败:', error);
      throw error instanceof LLMServiceError ? error : new LLMServiceError(
        `聊天请求处理失败: ${error instanceof Error ? error.message : String(error)}`,
        500
      );
    }
  }

  /**
   * 验证聊天请求
   */
  private validateChatRequest(request: LLMChatRequest): void {
    if (!request.messages || request.messages.length === 0) {
      throw new LLMServiceError('消息列表不能为空', 400);
    }

    if (!request.model) {
      throw new LLMServiceError('模型参数不能为空', 400);
    }

    if (!this.llmClients.has(request.model)) {
      throw new LLMServiceError(`不支持的模型: ${request.model}`, 400);
    }

    // 验证消息格式
    for (const message of request.messages) {
      if (!message.role || !message.content) {
        throw new LLMServiceError('消息格式无效：role和content字段必填', 400);
      }

      if (!['user', 'assistant', 'system'].includes(message.role)) {
        throw new LLMServiceError(`无效的消息角色: ${message.role}`, 400);
      }
    }
  }

  /**
   * 获取或创建对话
   */
  private async getOrCreateConversation(conversationId?: string): Promise<LLMConversation> {
    if (conversationId) {
      // 尝试获取现有对话
      const existing = await this.prisma.lLMConversation.findUnique({
        where: { id: conversationId }
      });

      if (existing) {
        return {
          id: existing.id,
          title: existing.title || undefined,
          userId: existing.userId || undefined,
          createdAt: existing.createdAt.toISOString(),
          updatedAt: existing.updatedAt.toISOString()
        };
      }
    }

    // 创建新对话
    const conversation = await this.prisma.lLMConversation.create({
      data: {
        id: conversationId || `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title: null,
        userId: null
      }
    });

    console.log(`📝 创建新对话: ${conversation.id}`);

    return {
      id: conversation.id,
      title: conversation.title || undefined,
      userId: conversation.userId || undefined,
      createdAt: conversation.createdAt.toISOString(),
      updatedAt: conversation.updatedAt.toISOString()
    };
  }

  /**
   * 保存用户消息
   */
  private async saveUserMessage(
    conversationId: string, 
    request: LLMChatRequest
  ): Promise<LLMMessageRecord> {
    const userMessage = request.messages[request.messages.length - 1];
    
    if (userMessage.role !== 'user') {
      throw new LLMServiceError('最后一条消息必须是用户消息', 400);
    }

    const message = await this.prisma.lLMMessage.create({
      data: {
        conversationId,
        role: userMessage.role,
        content: userMessage.content,
        model: request.model,
        requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        deepThinking: request.options?.deep_thinking || false,
        onlineSearch: request.options?.online_search || false,
        metadata: JSON.stringify({
          temperature: request.options?.temperature,
          max_tokens: request.options?.max_tokens
        })
      }
    });

    return {
      id: message.id,
      conversationId: message.conversationId,
      role: message.role as any,
      content: message.content,
      model: message.model as LLMModelType,
      parentId: message.parentId || undefined,
      requestId: message.requestId || undefined,
      promptTokens: message.promptTokens || undefined,
      completionTokens: message.completionTokens || undefined,
      totalTokens: message.totalTokens || undefined,
      finishReason: message.finishReason as any,
      deepThinking: message.deepThinking,
      onlineSearch: message.onlineSearch,
      metadata: message.metadata ? JSON.parse(message.metadata) : undefined,
      createdAt: message.createdAt.toISOString()
    };
  }

  /**
   * 保存助手回复
   */
  private async saveAssistantMessage(
    conversationId: string,
    llmResponse: LLMChatResponse,
    parentMessageId: string,
    request: LLMChatRequest
  ): Promise<LLMMessageRecord> {
    const message = await this.prisma.lLMMessage.create({
      data: {
        conversationId,
        role: 'assistant',
        content: llmResponse.data.message.content,
        model: llmResponse.data.model,
        parentId: parentMessageId,
        requestId: llmResponse.data.request_id,
        promptTokens: llmResponse.data.usage.prompt_tokens,
        completionTokens: llmResponse.data.usage.completion_tokens,
        totalTokens: llmResponse.data.usage.total_tokens,
        finishReason: llmResponse.data.finish_reason,
        deepThinking: request.options?.deep_thinking || false,
        onlineSearch: request.options?.online_search || false,
        metadata: JSON.stringify({
          temperature: request.options?.temperature,
          max_tokens: request.options?.max_tokens
        })
      }
    });

    return this.convertToMessageRecord(message);
  }

  /**
   * 转换数据库记录为消息记录类型
   */
  private convertToMessageRecord(message: any): LLMMessageRecord {
    return {
      id: message.id,
      conversationId: message.conversationId,
      role: message.role as any,
      content: message.content,
      model: message.model as LLMModelType,
      parentId: message.parentId || undefined,
      requestId: message.requestId || undefined,
      promptTokens: message.promptTokens || undefined,
      completionTokens: message.completionTokens || undefined,
      totalTokens: message.totalTokens || undefined,
      finishReason: message.finishReason as any,
      deepThinking: message.deepThinking,
      onlineSearch: message.onlineSearch,
      metadata: message.metadata ? JSON.parse(message.metadata) : undefined,
      createdAt: message.createdAt.toISOString()
    };
  }

  /**
   * 获取LLM客户端
   */
  private getLLMClient(model: LLMModelType): EnhancedLLMClient {
    const client = this.llmClients.get(model);
    if (!client) {
      throw new LLMServiceError(`未找到模型 ${model} 的客户端`, 500);
    }
    return client;
  }

  /**
   * 准备上下文消息
   */
  private async prepareContextMessages(conversationId: string, request: LLMChatRequest): Promise<any[]> {
    // 如果请求中包含完整的消息历史，直接使用
    if (request.messages.length > 1) {
      return request.messages;
    }

    // 否则从数据库获取对话历史
    const messages = await this.prisma.lLMMessage.findMany({
      where: { conversationId },
      orderBy: { createdAt: 'asc' },
      take: 20 // 限制上下文长度
    });

    // 转换为LLM API格式
    const contextMessages = messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    // 添加当前用户消息
    contextMessages.push(request.messages[0]);

    return contextMessages;
  }

  /**
   * 更新对话标题
   */
  private async updateConversationTitle(conversation: LLMConversation, firstMessage?: string): Promise<void> {
    // 如果对话已有标题或没有首条消息，跳过
    if (conversation.title || !firstMessage) {
      return;
    }

    // 生成对话标题（取前30个字符）
    const title = firstMessage.length > 30
      ? firstMessage.substring(0, 30) + '...'
      : firstMessage;

    await this.prisma.lLMConversation.update({
      where: { id: conversation.id },
      data: { title }
    });

    console.log(`📝 更新对话标题: ${title}`);
  }

  /**
   * 获取对话列表
   */
  async getConversations(userId?: string, limit: number = 50): Promise<LLMConversation[]> {
    const conversations = await this.prisma.lLMConversation.findMany({
      where: userId ? { userId } : {},
      orderBy: { updatedAt: 'desc' },
      take: limit,
      include: {
        messages: {
          take: 1,
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    return conversations.map(conv => ({
      id: conv.id,
      title: conv.title || undefined,
      userId: conv.userId || undefined,
      createdAt: conv.createdAt.toISOString(),
      updatedAt: conv.updatedAt.toISOString()
    }));
  }

  /**
   * 获取对话消息
   */
  async getConversationMessages(conversationId: string, limit: number = 100): Promise<LLMMessageRecord[]> {
    const messages = await this.prisma.lLMMessage.findMany({
      where: { conversationId },
      orderBy: { createdAt: 'asc' },
      take: limit
    });

    return messages.map(msg => this.convertToMessageRecord(msg));
  }

  /**
   * 删除对话
   */
  async deleteConversation(conversationId: string): Promise<void> {
    await this.prisma.lLMConversation.delete({
      where: { id: conversationId }
    });

    console.log(`🗑️ 删除对话: ${conversationId}`);
  }

  /**
   * 获取支持的模型列表
   */
  getSupportedModels(): LLMModelType[] {
    return Array.from(this.llmClients.keys()) as LLMModelType[];
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{ [model: string]: boolean }> {
    const results: { [model: string]: boolean } = {};

    for (const [model, client] of this.llmClients) {
      try {
        results[model] = await client.healthCheck();
      } catch (error) {
        console.error(`模型 ${model} 健康检查失败:`, error);
        results[model] = false;
      }
    }

    return results;
  }
}
