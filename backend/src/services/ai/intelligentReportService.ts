import { PrismaClient } from '@prisma/client';
import {
  ReportGenerationRequest,
  ReportGenerationResponse,
  ReportInstance,
  ReportTemplate,
  UserProfile,
  DataInsight,
  GeneratedReportSection,
  UserRole,
  ReportStatus,
  DatabaseError,
  ExternalServiceError
} from '../../types';
import { AIService } from '../aiService';
import { RedisClientWrapper } from '../../utils/redisClient';

/**
 * 智能报表生成服务
 * 负责基于用户角色和业务需求自动生成个性化报表
 */
export class IntelligentReportService {
  private prisma: PrismaClient;
  private aiService: AIService;
  private redisClient: RedisClientWrapper;

  constructor(
    prisma: PrismaClient,
    aiService: AIService,
    redisClient: RedisClientWrapper
  ) {
    this.prisma = prisma;
    this.aiService = aiService;
    this.redisClient = redisClient;
  }

  /**
   * 生成角色化智能报表
   * 根据用户角色、业务领域和数据特征生成个性化报表
   */
  async generateRoleBasedReport(request: ReportGenerationRequest): Promise<ReportGenerationResponse> {
    try {
      console.log(`🚀 开始生成智能报表 - 用户: ${request.userId}`);

      // 1. 获取用户配置信息
      const userProfile = await this.getUserProfile(request.userId);
      console.log(`👤 获取用户配置 - 角色: ${userProfile.role}`);

      // 2. 获取或创建报表模板
      const template = request.templateId 
        ? await this.getReportTemplate(request.templateId)
        : await this.createDefaultTemplate(userProfile.role, request.config?.dataSourceIds || []);

      // 3. 创建报表实例记录
      const reportInstance = await this.createReportInstance(request, template.id);
      console.log(`📄 创建报表实例: ${reportInstance.id}`);

      // 4. 异步生成报表内容
      this.generateReportContentAsync(reportInstance.id, userProfile, template, request);

      return {
        reportId: reportInstance.id,
        status: 'generating' as ReportStatus,
        estimatedCompletionTime: this.estimateCompletionTime(template),
        previewUrl: `/api/reports/${reportInstance.id}/preview`
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 报表生成失败:', errorMessage);
      throw new ExternalServiceError(`智能报表生成失败: ${errorMessage}`);
    }
  }

  /**
   * 获取用户配置信息
   */
  private async getUserProfile(userId: string): Promise<UserProfile> {
    try {
      let userProfile = await this.prisma.userProfile.findUnique({
        where: { userId }
      });

      // 如果用户配置不存在，创建默认配置
      if (!userProfile) {
        console.log(`🔧 为用户 ${userId} 创建默认配置`);
        userProfile = await this.prisma.userProfile.create({
          data: {
            userId,
            role: 'analyst', // 默认角色
            preferences: JSON.stringify({
              language: 'zh-CN',
              timezone: 'Asia/Shanghai',
              reportFormat: 'comprehensive',
              notificationSettings: {
                email: true,
                push: false
              }
            }),
            queryHistory: JSON.stringify({
              recentQueries: [],
              favoriteTopics: [],
              analysisPatterns: []
            })
          }
        });
      }

      return {
        id: userProfile.id,
        userId: userProfile.userId,
        role: userProfile.role as UserRole,
        preferences: JSON.parse(userProfile.preferences),
        queryHistory: userProfile.queryHistory ? JSON.parse(userProfile.queryHistory) : undefined,
        createdAt: userProfile.createdAt.toISOString(),
        updatedAt: userProfile.updatedAt.toISOString()
      };
    } catch (error) {
      throw new DatabaseError('获取用户配置失败');
    }
  }

  /**
   * 获取报表模板
   */
  private async getReportTemplate(templateId: string): Promise<ReportTemplate> {
    try {
      const template = await this.prisma.reportTemplate.findUnique({
        where: { id: templateId }
      });

      if (!template) {
        throw new Error('报表模板不存在');
      }

      return {
        id: template.id,
        name: template.name,
        description: template.description || undefined,
        config: JSON.parse(template.config),
        userRole: template.userRole as UserRole,
        domain: template.domain,
        isPublic: template.isPublic,
        createdBy: template.createdBy,
        createdAt: template.createdAt.toISOString(),
        updatedAt: template.updatedAt.toISOString()
      };
    } catch (error) {
      throw new DatabaseError('获取报表模板失败');
    }
  }

  /**
   * 创建默认报表模板
   */
  private async createDefaultTemplate(userRole: UserRole, dataSourceIds: string[]): Promise<ReportTemplate> {
    try {
      console.log(`🎨 为角色 ${userRole} 创建默认报表模板`);

      const templateConfig = this.getDefaultTemplateConfig(userRole, dataSourceIds);
      
      const template = await this.prisma.reportTemplate.create({
        data: {
          name: `${userRole}默认报表模板`,
          description: `为${userRole}角色自动生成的默认报表模板`,
          config: JSON.stringify(templateConfig),
          userRole,
          domain: 'general',
          isPublic: false,
          createdBy: 'system'
        }
      });

      return {
        id: template.id,
        name: template.name,
        description: template.description || undefined,
        config: templateConfig,
        userRole: template.userRole as UserRole,
        domain: template.domain,
        isPublic: template.isPublic,
        createdBy: template.createdBy,
        createdAt: template.createdAt.toISOString(),
        updatedAt: template.updatedAt.toISOString()
      };
    } catch (error) {
      throw new DatabaseError('创建默认报表模板失败');
    }
  }

  /**
   * 获取角色默认模板配置
   */
  private getDefaultTemplateConfig(userRole: UserRole, dataSourceIds: string[]) {
    const baseConfig = {
      dataSourceIds,
      refreshInterval: 3600, // 1小时
      exportFormats: ['pdf', 'html', 'excel']
    };

    switch (userRole) {
      case 'executive':
        return {
          ...baseConfig,
          sections: [
            {
              id: 'executive-summary',
              title: '执行摘要',
              type: 'summary' as const,
              config: { includeKPIs: true, includeAlerts: true },
              order: 1
            },
            {
              id: 'key-metrics',
              title: '关键指标',
              type: 'chart' as const,
              config: { chartType: 'dashboard', showTrends: true },
              order: 2
            },
            {
              id: 'strategic-insights',
              title: '战略洞察',
              type: 'insight' as const,
              config: { focusLevel: 'strategic' },
              order: 3
            }
          ]
        };

      case 'manager':
        return {
          ...baseConfig,
          sections: [
            {
              id: 'performance-overview',
              title: '绩效概览',
              type: 'summary' as const,
              config: { includeComparisons: true },
              order: 1
            },
            {
              id: 'team-metrics',
              title: '团队指标',
              type: 'chart' as const,
              config: { chartType: 'multi-series', groupBy: 'team' },
              order: 2
            },
            {
              id: 'operational-insights',
              title: '运营洞察',
              type: 'insight' as const,
              config: { focusLevel: 'operational' },
              order: 3
            },
            {
              id: 'action-items',
              title: '行动项目',
              type: 'table' as const,
              config: { showPriority: true, showOwner: true },
              order: 4
            }
          ]
        };

      case 'analyst':
      default:
        return {
          ...baseConfig,
          sections: [
            {
              id: 'data-summary',
              title: '数据摘要',
              type: 'summary' as const,
              config: { includeDataQuality: true },
              order: 1
            },
            {
              id: 'detailed-analysis',
              title: '详细分析',
              type: 'chart' as const,
              config: { chartType: 'detailed', showStatistics: true },
              order: 2
            },
            {
              id: 'data-insights',
              title: '数据洞察',
              type: 'insight' as const,
              config: { focusLevel: 'analytical', includeMethodology: true },
              order: 3
            },
            {
              id: 'raw-data',
              title: '原始数据',
              type: 'table' as const,
              config: { showFilters: true, allowExport: true },
              order: 4
            }
          ]
        };
    }
  }

  /**
   * 创建报表实例
   */
  private async createReportInstance(
    request: ReportGenerationRequest,
    templateId: string
  ): Promise<ReportInstance> {
    try {
      const reportInstance = await this.prisma.reportInstance.create({
        data: {
          templateId,
          userId: request.userId,
          title: request.title || `智能报表 - ${new Date().toLocaleDateString('zh-CN')}`,
          content: JSON.stringify({
            executiveSummary: '',
            sections: [],
            keyInsights: [],
            actionItems: [],
            metadata: {
              generationTime: 0,
              dataFreshness: '',
              confidence: 0
            }
          }),
          metadata: JSON.stringify({
            generationDuration: 0,
            dataSourcesUsed: request.config?.dataSourceIds || [],
            aiModelVersion: 'gpt-3.5-turbo'
          }),
          status: 'generating'
        }
      });

      return {
        id: reportInstance.id,
        templateId: reportInstance.templateId,
        userId: reportInstance.userId,
        title: reportInstance.title,
        content: JSON.parse(reportInstance.content),
        metadata: JSON.parse(reportInstance.metadata || '{}'),
        status: reportInstance.status as ReportStatus,
        generatedAt: reportInstance.generatedAt.toISOString(),
        expiresAt: reportInstance.expiresAt?.toISOString()
      };
    } catch (error) {
      throw new DatabaseError('创建报表实例失败');
    }
  }

  /**
   * 估算报表生成完成时间（秒）
   */
  private estimateCompletionTime(template: ReportTemplate): number {
    const basetime = 30; // 基础时间30秒
    const sectionTime = template.config.sections.length * 10; // 每个章节10秒
    const dataSourceTime = template.config.dataSourceIds.length * 5; // 每个数据源5秒
    
    return basetime + sectionTime + dataSourceTime;
  }

  /**
   * 异步生成报表内容
   */
  private async generateReportContentAsync(
    reportId: string,
    userProfile: UserProfile,
    template: ReportTemplate,
    request: ReportGenerationRequest
  ): Promise<void> {
    try {
      console.log(`🔄 开始异步生成报表内容: ${reportId}`);
      const startTime = Date.now();

      // 1. 获取相关数据源的数据
      const relevantData = await this.getRoleRelevantData(userProfile, template.config.dataSourceIds);
      console.log(`📊 获取相关数据完成`);

      // 2. AI生成报表大纲
      const outline = await this.generateReportOutline(userProfile.role, relevantData, template);
      console.log(`📋 生成报表大纲完成`);

      // 3. 生成具体章节内容
      const sections = await this.generateReportSections(outline, relevantData, userProfile);
      console.log(`📝 生成章节内容完成`);

      // 4. 生成关键洞察
      const insights = await this.generateKeyInsights(relevantData, userProfile.role);
      console.log(`💡 生成关键洞察完成`);

      // 5. 生成执行摘要
      const executiveSummary = await this.generateExecutiveSummary(sections, insights, userProfile.role);
      console.log(`📄 生成执行摘要完成`);

      // 6. 更新报表实例
      const generationTime = Date.now() - startTime;
      await this.updateReportInstance(reportId, {
        executiveSummary,
        sections,
        keyInsights: insights,
        actionItems: await this.generateActionItems(insights, userProfile.role),
        metadata: {
          generationTime,
          dataFreshness: new Date().toISOString(),
          confidence: this.calculateOverallConfidence(insights)
        }
      }, generationTime);

      console.log(`✅ 报表生成完成: ${reportId}, 耗时: ${generationTime}ms`);

    } catch (error) {
      console.error(`❌ 异步报表生成失败: ${reportId}`, error);

      // 更新报表状态为失败
      await this.prisma.reportInstance.update({
        where: { id: reportId },
        data: { status: 'failed' }
      });
    }
  }

  /**
   * 获取角色相关数据
   */
  private async getRoleRelevantData(userProfile: UserProfile, dataSourceIds: string[]): Promise<any> {
    try {
      // 模拟获取数据源数据的过程
      // 在实际实现中，这里会根据数据源类型执行相应的查询
      console.log(`📊 获取数据源数据: ${dataSourceIds.join(', ')}`);

      return {
        dataSources: dataSourceIds,
        sampleData: {
          sales: [
            { month: '2024-01', amount: 150000, growth: 0.15 },
            { month: '2024-02', amount: 165000, growth: 0.10 },
            { month: '2024-03', amount: 180000, growth: 0.09 }
          ],
          customers: [
            { segment: '企业客户', count: 120, satisfaction: 4.2 },
            { segment: '个人客户', count: 850, satisfaction: 3.8 }
          ],
          performance: {
            revenue: 495000,
            profit: 89100,
            margin: 0.18
          }
        },
        metadata: {
          lastUpdated: new Date().toISOString(),
          recordCount: 1000,
          dataQuality: 0.95
        }
      };
    } catch (error) {
      throw new DatabaseError('获取角色相关数据失败');
    }
  }

  /**
   * AI生成报表大纲
   */
  private async generateReportOutline(
    userRole: UserRole,
    relevantData: any,
    template: ReportTemplate
  ): Promise<any> {
    try {
      const prompt = `
作为一个专业的商业分析师，请为${userRole}角色生成一个智能报表大纲。

用户角色: ${userRole}
业务领域: ${template.domain}
数据概况: ${JSON.stringify(relevantData.metadata, null, 2)}

请生成一个结构化的报表大纲，包括：
1. 报表标题
2. 主要章节
3. 每个章节的重点内容
4. 关键指标和洞察点

请用中文回答，格式要专业且易于理解。
`;

      const response = await this.aiService.generateNaturalLanguageResponse(
        prompt,
        relevantData,
        '生成报表大纲'
      );

      return {
        title: `${userRole}智能分析报表`,
        sections: template.config.sections,
        aiGeneratedOutline: response,
        focusAreas: this.getRoleFocusAreas(userRole)
      };
    } catch (error) {
      console.error('AI生成报表大纲失败:', error);
      return {
        title: `${userRole}智能分析报表`,
        sections: template.config.sections,
        aiGeneratedOutline: '报表大纲生成失败，使用默认模板',
        focusAreas: this.getRoleFocusAreas(userRole)
      };
    }
  }

  /**
   * 获取角色关注领域
   */
  private getRoleFocusAreas(userRole: UserRole): string[] {
    switch (userRole) {
      case 'executive':
        return ['战略目标达成', '市场表现', '财务健康度', '风险管控', '增长机会'];
      case 'manager':
        return ['团队绩效', '运营效率', '成本控制', '客户满意度', '流程优化'];
      case 'analyst':
      default:
        return ['数据质量', '趋势分析', '异常检测', '相关性分析', '预测模型'];
    }
  }

  /**
   * 生成报表章节内容
   */
  private async generateReportSections(
    outline: any,
    relevantData: any,
    userProfile: UserProfile
  ): Promise<GeneratedReportSection[]> {
    try {
      const sections: GeneratedReportSection[] = [];

      for (const sectionConfig of outline.sections) {
        console.log(`📝 生成章节: ${sectionConfig.title}`);

        const section: GeneratedReportSection = {
          id: sectionConfig.id,
          title: sectionConfig.title,
          type: sectionConfig.type,
          content: await this.generateSectionContent(sectionConfig, relevantData, userProfile),
          order: sectionConfig.order
        };

        sections.push(section);
      }

      return sections.sort((a, b) => a.order - b.order);
    } catch (error) {
      throw new ExternalServiceError('生成报表章节失败');
    }
  }

  /**
   * 生成章节具体内容
   */
  private async generateSectionContent(
    sectionConfig: any,
    relevantData: any,
    userProfile: UserProfile
  ): Promise<any> {
    try {
      switch (sectionConfig.type) {
        case 'summary':
          return await this.generateSummaryContent(sectionConfig, relevantData, userProfile);
        case 'chart':
          return await this.generateChartContent(sectionConfig, relevantData);
        case 'table':
          return await this.generateTableContent(sectionConfig, relevantData);
        case 'insight':
          return await this.generateInsightContent(sectionConfig, relevantData, userProfile);
        case 'text':
          return await this.generateTextContent(sectionConfig, relevantData, userProfile);
        default:
          return { text: '暂不支持的章节类型' };
      }
    } catch (error) {
      console.error(`生成章节内容失败: ${sectionConfig.title}`, error);
      return { text: '章节内容生成失败' };
    }
  }

  /**
   * 生成摘要内容
   */
  private async generateSummaryContent(
    sectionConfig: any,
    relevantData: any,
    userProfile: UserProfile
  ): Promise<any> {
    const prompt = `
请为${userProfile.role}角色生成${sectionConfig.title}章节的摘要内容。

数据概况:
- 收入: ${relevantData.sampleData.performance.revenue}
- 利润: ${relevantData.sampleData.performance.profit}
- 利润率: ${(relevantData.sampleData.performance.margin * 100).toFixed(1)}%
- 客户总数: ${relevantData.sampleData.customers.reduce((sum: number, c: any) => sum + c.count, 0)}

请生成一个简洁但全面的摘要，突出关键指标和趋势。用中文回答。
`;

    try {
      const aiResponse = await this.aiService.generateNaturalLanguageResponse(
        prompt,
        relevantData,
        '生成摘要内容'
      );

      return {
        text: aiResponse,
        keyMetrics: [
          { name: '总收入', value: relevantData.sampleData.performance.revenue, unit: '元' },
          { name: '净利润', value: relevantData.sampleData.performance.profit, unit: '元' },
          { name: '利润率', value: (relevantData.sampleData.performance.margin * 100).toFixed(1), unit: '%' }
        ]
      };
    } catch (error) {
      return {
        text: '本期业务表现良好，各项关键指标均达到预期目标。',
        keyMetrics: [
          { name: '总收入', value: relevantData.sampleData.performance.revenue, unit: '元' },
          { name: '净利润', value: relevantData.sampleData.performance.profit, unit: '元' },
          { name: '利润率', value: (relevantData.sampleData.performance.margin * 100).toFixed(1), unit: '%' }
        ]
      };
    }
  }

  /**
   * 生成图表内容
   */
  private async generateChartContent(sectionConfig: any, relevantData: any): Promise<any> {
    try {
      // 根据数据推荐合适的图表类型
      const chartType = await this.aiService.recommendVisualization(
        `显示${sectionConfig.title}的数据`,
        relevantData.sampleData
      );

      return {
        data: relevantData.sampleData.sales,
        chart: {
          type: chartType || 'line',
          spec: {
            mark: 'line',
            encoding: {
              x: { field: 'month', type: 'temporal', title: '月份' },
              y: { field: 'amount', type: 'quantitative', title: '销售额' }
            },
            title: sectionConfig.title
          }
        }
      };
    } catch (error) {
      return {
        data: relevantData.sampleData.sales,
        chart: {
          type: 'line',
          spec: {
            mark: 'line',
            encoding: {
              x: { field: 'month', type: 'temporal', title: '月份' },
              y: { field: 'amount', type: 'quantitative', title: '销售额' }
            },
            title: sectionConfig.title
          }
        }
      };
    }
  }

  /**
   * 生成表格内容
   */
  private async generateTableContent(sectionConfig: any, relevantData: any): Promise<any> {
    return {
      data: relevantData.sampleData.customers,
      columns: [
        { key: 'segment', title: '客户细分', type: 'string' },
        { key: 'count', title: '客户数量', type: 'number' },
        { key: 'satisfaction', title: '满意度', type: 'number' }
      ],
      pagination: {
        page: 1,
        pageSize: 10,
        total: relevantData.sampleData.customers.length
      }
    };
  }

  /**
   * 生成洞察内容
   */
  private async generateInsightContent(
    sectionConfig: any,
    relevantData: any,
    userProfile: UserProfile
  ): Promise<any> {
    const insights = await this.generateKeyInsights(relevantData, userProfile.role);

    return {
      insights: insights.map(insight => insight.description),
      recommendations: insights
        .filter(insight => insight.metadata?.recommendations)
        .flatMap(insight => insight.metadata!.recommendations!)
    };
  }

  /**
   * 生成文本内容
   */
  private async generateTextContent(
    sectionConfig: any,
    relevantData: any,
    userProfile: UserProfile
  ): Promise<any> {
    const prompt = `
请为${userProfile.role}角色生成${sectionConfig.title}章节的详细文本内容。

基于以下数据生成专业的分析文本:
${JSON.stringify(relevantData.sampleData, null, 2)}

请用中文回答，内容要专业、准确、有洞察力。
`;

    try {
      const aiResponse = await this.aiService.generateNaturalLanguageResponse(
        prompt,
        relevantData,
        '生成文本内容'
      );

      return { text: aiResponse };
    } catch (error) {
      return { text: '详细分析内容生成中，请稍后查看完整报表。' };
    }
  }

  /**
   * 生成关键洞察
   */
  private async generateKeyInsights(relevantData: any, userRole: UserRole): Promise<DataInsight[]> {
    try {
      const insights: DataInsight[] = [];

      // 基于数据生成不同类型的洞察
      const salesData = relevantData.sampleData.sales;
      const lastMonth = salesData[salesData.length - 1];
      const previousMonth = salesData[salesData.length - 2];

      // 趋势洞察
      if (lastMonth && previousMonth) {
        const growthRate = (lastMonth.amount - previousMonth.amount) / previousMonth.amount;

        insights.push({
          id: `insight-trend-${Date.now()}`,
          dataSourceId: relevantData.dataSources[0] || 'default',
          type: 'trend',
          title: '销售增长趋势',
          description: `本月销售额为${lastMonth.amount.toLocaleString()}元，环比${growthRate > 0 ? '增长' : '下降'}${Math.abs(growthRate * 100).toFixed(1)}%`,
          confidence: 0.85,
          severity: growthRate > 0.1 ? 'high' : growthRate < -0.1 ? 'critical' : 'medium',
          status: 'new',
          metadata: {
            affectedMetrics: ['销售额', '增长率'],
            timeRange: {
              start: previousMonth.month,
              end: lastMonth.month
            },
            recommendations: growthRate > 0
              ? ['继续保持当前策略', '考虑扩大市场投入']
              : ['分析下降原因', '制定改进措施']
          },
          createdAt: new Date().toISOString()
        });
      }

      // 客户洞察
      const customerData = relevantData.sampleData.customers;
      const totalCustomers = customerData.reduce((sum: number, c: any) => sum + c.count, 0);
      const avgSatisfaction = customerData.reduce((sum: number, c: any) => sum + c.satisfaction * c.count, 0) / totalCustomers;

      insights.push({
        id: `insight-customer-${Date.now()}`,
        dataSourceId: relevantData.dataSources[0] || 'default',
        type: 'opportunity',
        title: '客户满意度分析',
        description: `当前客户总数${totalCustomers}，平均满意度${avgSatisfaction.toFixed(1)}分`,
        confidence: 0.78,
        severity: avgSatisfaction > 4.0 ? 'low' : avgSatisfaction < 3.5 ? 'high' : 'medium',
        status: 'new',
        metadata: {
          affectedMetrics: ['客户满意度', '客户数量'],
          recommendations: avgSatisfaction < 4.0
            ? ['提升服务质量', '加强客户关系管理']
            : ['维持当前服务水平', '探索客户增值服务']
        },
        createdAt: new Date().toISOString()
      });

      // 根据用户角色过滤和调整洞察
      return this.filterInsightsByRole(insights, userRole);

    } catch (error) {
      console.error('生成关键洞察失败:', error);
      return [];
    }
  }

  /**
   * 根据用户角色过滤洞察
   */
  private filterInsightsByRole(insights: DataInsight[], userRole: UserRole): DataInsight[] {
    switch (userRole) {
      case 'executive':
        // 高管关注战略级洞察
        return insights.filter(insight =>
          insight.severity === 'high' || insight.severity === 'critical' ||
          insight.type === 'opportunity'
        );
      case 'manager':
        // 经理关注运营级洞察
        return insights.filter(insight =>
          insight.severity !== 'low' || insight.type === 'trend'
        );
      case 'analyst':
      default:
        // 分析师查看所有洞察
        return insights;
    }
  }

  /**
   * 生成执行摘要
   */
  private async generateExecutiveSummary(
    sections: GeneratedReportSection[],
    insights: DataInsight[],
    userRole: UserRole
  ): Promise<string> {
    try {
      const prompt = `
作为一个专业的商业分析师，请为${userRole}角色生成一个简洁而全面的执行摘要。

基于以下信息:
1. 报表章节数量: ${sections.length}
2. 关键洞察数量: ${insights.length}
3. 主要洞察: ${insights.map(i => i.title).join(', ')}

请生成一个200-300字的执行摘要，突出最重要的发现和建议。用中文回答。
`;

      const aiResponse = await this.aiService.generateNaturalLanguageResponse(
        prompt,
        { sections, insights },
        '生成执行摘要'
      );

      return aiResponse;
    } catch (error) {
      console.error('生成执行摘要失败:', error);
      return `本期报表包含${sections.length}个主要章节，发现${insights.length}个关键洞察。整体业务表现${insights.some(i => i.severity === 'critical') ? '需要关注' : '良好'}，建议重点关注${insights.filter(i => i.severity === 'high' || i.severity === 'critical').map(i => i.title).join('、')}等方面。`;
    }
  }

  /**
   * 生成行动项目
   */
  private async generateActionItems(insights: DataInsight[], userRole: UserRole): Promise<string[]> {
    try {
      const actionItems: string[] = [];

      // 基于洞察生成行动项目
      for (const insight of insights) {
        if (insight.metadata?.recommendations) {
          actionItems.push(...insight.metadata.recommendations);
        }
      }

      // 根据角色调整行动项目
      const roleSpecificActions = this.getRoleSpecificActions(userRole, insights);
      actionItems.push(...roleSpecificActions);

      // 去重并限制数量
      return [...new Set(actionItems)].slice(0, 8);
    } catch (error) {
      console.error('生成行动项目失败:', error);
      return ['定期监控关键指标', '持续优化业务流程'];
    }
  }

  /**
   * 获取角色特定的行动项目
   */
  private getRoleSpecificActions(userRole: UserRole, insights: DataInsight[]): string[] {
    const criticalInsights = insights.filter(i => i.severity === 'critical' || i.severity === 'high');

    switch (userRole) {
      case 'executive':
        return [
          '制定下季度战略规划',
          '评估市场扩张机会',
          criticalInsights.length > 0 ? '召开紧急管理层会议' : '维持当前战略方向'
        ];
      case 'manager':
        return [
          '优化团队工作流程',
          '制定绩效改进计划',
          '加强跨部门协作'
        ];
      case 'analyst':
      default:
        return [
          '深入分析数据异常',
          '完善数据收集流程',
          '建立预警机制'
        ];
    }
  }

  /**
   * 计算整体置信度
   */
  private calculateOverallConfidence(insights: DataInsight[]): number {
    if (insights.length === 0) return 0.5;

    const totalConfidence = insights.reduce((sum, insight) => sum + insight.confidence, 0);
    return totalConfidence / insights.length;
  }

  /**
   * 更新报表实例
   */
  private async updateReportInstance(
    reportId: string,
    content: any,
    generationTime: number
  ): Promise<void> {
    try {
      await this.prisma.reportInstance.update({
        where: { id: reportId },
        data: {
          content: JSON.stringify(content),
          metadata: JSON.stringify({
            generationDuration: generationTime,
            dataSourcesUsed: content.metadata?.dataSourcesUsed || [],
            aiModelVersion: 'gpt-3.5-turbo'
          }),
          status: 'completed'
        }
      });
    } catch (error) {
      throw new DatabaseError('更新报表实例失败');
    }
  }

  /**
   * 获取报表实例
   */
  async getReportInstance(reportId: string): Promise<ReportInstance | null> {
    try {
      const reportInstance = await this.prisma.reportInstance.findUnique({
        where: { id: reportId }
      });

      if (!reportInstance) {
        return null;
      }

      return {
        id: reportInstance.id,
        templateId: reportInstance.templateId,
        userId: reportInstance.userId,
        title: reportInstance.title,
        content: JSON.parse(reportInstance.content),
        metadata: JSON.parse(reportInstance.metadata || '{}'),
        status: reportInstance.status as ReportStatus,
        generatedAt: reportInstance.generatedAt.toISOString(),
        expiresAt: reportInstance.expiresAt?.toISOString()
      };
    } catch (error) {
      throw new DatabaseError('获取报表实例失败');
    }
  }

  /**
   * 获取用户的报表列表
   */
  async getUserReports(userId: string, limit: number = 10): Promise<ReportInstance[]> {
    try {
      const reports = await this.prisma.reportInstance.findMany({
        where: { userId },
        orderBy: { generatedAt: 'desc' },
        take: limit
      });

      return reports.map(report => ({
        id: report.id,
        templateId: report.templateId,
        userId: report.userId,
        title: report.title,
        content: JSON.parse(report.content),
        metadata: JSON.parse(report.metadata || '{}'),
        status: report.status as ReportStatus,
        generatedAt: report.generatedAt.toISOString(),
        expiresAt: report.expiresAt?.toISOString()
      }));
    } catch (error) {
      throw new DatabaseError('获取用户报表列表失败');
    }
  }
}
