{"name": "ai-bi-backend", "version": "1.0.0", "description": "AI-BI System Backend API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "npx prisma migrate dev", "migrate:deploy": "npx prisma migrate deploy", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio", "db:seed": "ts-node prisma/seed.ts", "db:init": "ts-node scripts/init-database.ts", "db:push": "npx prisma db push", "switch-db": "ts-node scripts/switch-database.ts", "test:sqlite": "ts-node scripts/test-sqlite.ts", "test:sqlite-simple": "ts-node scripts/simple-sqlite-test.ts"}, "keywords": ["api", "backend", "ai", "bi"], "author": "AI-BI Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "@types/sqlite3": "^3.1.11", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "openai": "^4.20.1", "pg": "^8.11.3", "redis": "^4.6.12", "sqlite3": "^5.1.7", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/pg": "^8.10.9", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^5.7.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}