import { Request, Response, NextFunction } from 'express';
import { LLMChatService } from '../services/llmChatService';
import { prisma } from '../lib/prisma';
import { 
  LLMChatRequest, 
  LLMServiceError, 
  ApiResponse 
} from '../types';

const llmChatService = new LLMChatService(prisma);

/**
 * 处理LLM聊天请求
 * POST /api/llm/chat
 */
export async function processLLMChat(req: Request, res: Response, next: NextFunction) {
  try {
    console.log('🚀 收到LLM聊天请求:', {
      model: req.body.model,
      messageCount: req.body.messages?.length,
      conversationId: req.body.conversation_id
    });

    // 验证请求体
    const chatRequest = validateLLMChatRequest(req.body);
    
    // 处理聊天请求
    const response = await llmChatService.processChat(chatRequest);
    
    console.log('✅ LLM聊天请求处理成功:', {
      conversationId: response.data.conversation_id,
      messageId: response.data.message_id,
      tokenUsage: response.data.usage.total_tokens
    });

    // 返回响应
    res.json(response);
  } catch (error) {
    console.error('❌ LLM聊天请求处理失败:', error);
    next(error);
  }
}

/**
 * 获取对话列表
 * GET /api/llm/conversations
 */
export async function getConversations(req: Request, res: Response, next: NextFunction) {
  try {
    const { limit } = req.query;
    const limitNumber = limit ? parseInt(limit as string) : 50;
    
    if (limitNumber > 100) {
      throw new LLMServiceError('limit参数不能超过100', 400);
    }

    const conversations = await llmChatService.getConversations(undefined, limitNumber);
    
    const apiResponse: ApiResponse = {
      success: true,
      data: conversations
    };
    
    res.json(apiResponse);
  } catch (error) {
    next(error);
  }
}

/**
 * 获取对话消息
 * GET /api/llm/conversations/:id/messages
 */
export async function getConversationMessages(req: Request, res: Response, next: NextFunction) {
  try {
    const { id } = req.params;
    const { limit } = req.query;
    
    if (!id) {
      throw new LLMServiceError('对话ID不能为空', 400);
    }
    
    const limitNumber = limit ? parseInt(limit as string) : 100;
    
    if (limitNumber > 200) {
      throw new LLMServiceError('limit参数不能超过200', 400);
    }

    const messages = await llmChatService.getConversationMessages(id, limitNumber);
    
    const apiResponse: ApiResponse = {
      success: true,
      data: messages
    };
    
    res.json(apiResponse);
  } catch (error) {
    next(error);
  }
}

/**
 * 创建新对话
 * POST /api/llm/conversations
 */
export async function createConversation(req: Request, res: Response, next: NextFunction) {
  try {
    const { title, userId } = req.body;
    
    // 创建一个临时的聊天请求来触发对话创建
    const tempRequest: LLMChatRequest = {
      messages: [{ role: 'user', content: title || '新对话' }],
      model: 'gpt-3.5-turbo',
      stream: false,
      conversation_id: `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    
    // 这里我们只创建对话，不实际发送到LLM
    const conversation = await (llmChatService as any).getOrCreateConversation(tempRequest.conversation_id);
    
    const apiResponse: ApiResponse = {
      success: true,
      data: conversation
    };
    
    res.json(apiResponse);
  } catch (error) {
    next(error);
  }
}

/**
 * 删除对话
 * DELETE /api/llm/conversations/:id
 */
export async function deleteConversation(req: Request, res: Response, next: NextFunction) {
  try {
    const { id } = req.params;
    
    if (!id) {
      throw new LLMServiceError('对话ID不能为空', 400);
    }

    await llmChatService.deleteConversation(id);
    
    const apiResponse: ApiResponse = {
      success: true,
      message: '对话删除成功'
    };
    
    res.json(apiResponse);
  } catch (error) {
    next(error);
  }
}

/**
 * 获取支持的模型列表
 * GET /api/llm/models
 */
export async function getSupportedModels(req: Request, res: Response, next: NextFunction) {
  try {
    const models = llmChatService.getSupportedModels();
    
    const apiResponse: ApiResponse = {
      success: true,
      data: models
    };
    
    res.json(apiResponse);
  } catch (error) {
    next(error);
  }
}

/**
 * LLM服务健康检查
 * GET /api/llm/health
 */
export async function llmHealthCheck(req: Request, res: Response, next: NextFunction) {
  try {
    const healthStatus = await llmChatService.healthCheck();
    
    const allHealthy = Object.values(healthStatus).every(status => status);
    
    const apiResponse: ApiResponse = {
      success: allHealthy,
      data: {
        status: allHealthy ? 'healthy' : 'degraded',
        models: healthStatus,
        timestamp: new Date().toISOString()
      }
    };
    
    res.status(allHealthy ? 200 : 503).json(apiResponse);
  } catch (error) {
    next(error);
  }
}

/**
 * 验证LLM聊天请求
 */
function validateLLMChatRequest(body: any): LLMChatRequest {
  // 基础字段验证
  if (!body.messages || !Array.isArray(body.messages) || body.messages.length === 0) {
    throw new LLMServiceError('messages字段必须是非空数组', 400);
  }

  if (!body.model || typeof body.model !== 'string') {
    throw new LLMServiceError('model字段必须是有效的字符串', 400);
  }

  if (typeof body.stream !== 'boolean') {
    throw new LLMServiceError('stream字段必须是布尔值', 400);
  }

  // 验证消息格式
  for (let i = 0; i < body.messages.length; i++) {
    const message = body.messages[i];
    
    if (!message.role || !message.content) {
      throw new LLMServiceError(`消息${i + 1}缺少必要字段：role和content`, 400);
    }

    if (!['user', 'assistant', 'system'].includes(message.role)) {
      throw new LLMServiceError(`消息${i + 1}的role字段无效：${message.role}`, 400);
    }

    if (typeof message.content !== 'string' || message.content.trim().length === 0) {
      throw new LLMServiceError(`消息${i + 1}的content字段必须是非空字符串`, 400);
    }
  }

  // 验证最后一条消息必须是用户消息
  const lastMessage = body.messages[body.messages.length - 1];
  if (lastMessage.role !== 'user') {
    throw new LLMServiceError('最后一条消息必须是用户消息', 400);
  }

  // 验证可选字段
  if (body.conversation_id && typeof body.conversation_id !== 'string') {
    throw new LLMServiceError('conversation_id字段必须是字符串', 400);
  }

  if (body.options) {
    if (body.options.temperature !== undefined) {
      const temp = parseFloat(body.options.temperature);
      if (isNaN(temp) || temp < 0 || temp > 2) {
        throw new LLMServiceError('temperature必须是0-2之间的数值', 400);
      }
    }

    if (body.options.max_tokens !== undefined) {
      const maxTokens = parseInt(body.options.max_tokens);
      if (isNaN(maxTokens) || maxTokens < 1 || maxTokens > 8000) {
        throw new LLMServiceError('max_tokens必须是1-8000之间的整数', 400);
      }
    }

    if (body.options.deep_thinking !== undefined && typeof body.options.deep_thinking !== 'boolean') {
      throw new LLMServiceError('deep_thinking字段必须是布尔值', 400);
    }

    if (body.options.online_search !== undefined && typeof body.options.online_search !== 'boolean') {
      throw new LLMServiceError('online_search字段必须是布尔值', 400);
    }
  }

  return {
    messages: body.messages,
    model: body.model,
    stream: body.stream,
    conversation_id: body.conversation_id,
    options: body.options
  };
}
