import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AppError } from '../types';

/**
 * JWT认证中间件接口
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email?: string;
    role?: string;
  };
}

/**
 * JWT Token载荷接口
 */
interface JWTPayload {
  id: string;
  email?: string;
  role?: string;
  iat?: number;
  exp?: number;
}

/**
 * JWT认证中间件
 * 支持开发模式下跳过JWT验证
 * 
 * 环境变量配置：
 * - DEV_MODE: 设置为 'true' 时在开发环境下跳过JWT验证
 * - SKIP_JWT_AUTH: 设置为 'true' 时跳过JWT验证（优先级高于DEV_MODE）
 * - JWT_SECRET: JWT签名密钥
 */
export function authenticateJWT(req: AuthenticatedRequest, res: Response, next: NextFunction): void {
  try {
    // 检查是否跳过JWT验证
    const skipJwtAuth = process.env.SKIP_JWT_AUTH === 'true';
    const devMode = process.env.DEV_MODE === 'true';
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    // 开发模式下跳过JWT验证的条件：
    // 1. SKIP_JWT_AUTH 明确设置为 true，或
    // 2. DEV_MODE 设置为 true 且当前环境为 development
    if (skipJwtAuth || (devMode && isDevelopment)) {
      console.log('🔓 开发模式：跳过JWT认证验证');
      
      // 在开发模式下，为请求添加一个默认的用户信息
      req.user = {
        id: 'dev-user-id',
        email: '<EMAIL>',
        role: 'admin'
      };
      
      return next();
    }
    
    // 生产模式下进行JWT验证
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      throw new AppError('缺少Authorization头', 401);
    }
    
    // 检查Authorization头格式：Bearer <token>
    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      throw new AppError('Authorization头格式错误，应为: Bearer <token>', 401);
    }
    
    const token = parts[1];
    
    if (!token) {
      throw new AppError('JWT token不能为空', 401);
    }
    
    // 验证JWT token
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('❌ JWT_SECRET环境变量未设置');
      throw new AppError('服务器配置错误', 500);
    }
    
    try {
      const decoded = jwt.verify(token, jwtSecret) as JWTPayload;
      
      // 将用户信息添加到请求对象中
      req.user = {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role
      };
      
      console.log(`✅ JWT验证成功，用户ID: ${decoded.id}`);
      next();
      
    } catch (jwtError: any) {
      console.error('❌ JWT验证失败:', jwtError.message);
      
      if (jwtError.name === 'TokenExpiredError') {
        throw new AppError('JWT token已过期', 401);
      } else if (jwtError.name === 'JsonWebTokenError') {
        throw new AppError('无效的JWT token', 401);
      } else {
        throw new AppError('JWT验证失败', 401);
      }
    }
    
  } catch (error) {
    next(error);
  }
}

/**
 * 可选的JWT认证中间件
 * 如果提供了token则验证，否则继续执行但不设置用户信息
 */
export function optionalAuthenticateJWT(req: AuthenticatedRequest, res: Response, next: NextFunction): void {
  try {
    // 检查是否跳过JWT验证
    const skipJwtAuth = process.env.SKIP_JWT_AUTH === 'true';
    const devMode = process.env.DEV_MODE === 'true';
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    if (skipJwtAuth || (devMode && isDevelopment)) {
      console.log('🔓 开发模式：跳过可选JWT认证验证');
      req.user = {
        id: 'dev-user-id',
        email: '<EMAIL>',
        role: 'admin'
      };
      return next();
    }
    
    const authHeader = req.headers.authorization;
    
    // 如果没有Authorization头，直接继续执行
    if (!authHeader) {
      return next();
    }
    
    // 如果有Authorization头，则尝试验证
    const parts = authHeader.split(' ');
    if (parts.length === 2 && parts[0] === 'Bearer') {
      const token = parts[1];
      const jwtSecret = process.env.JWT_SECRET;
      
      if (token && jwtSecret) {
        try {
          const decoded = jwt.verify(token, jwtSecret) as JWTPayload;
          req.user = {
            id: decoded.id,
            email: decoded.email,
            role: decoded.role
          };
          console.log(`✅ 可选JWT验证成功，用户ID: ${decoded.id}`);
        } catch (jwtError) {
          console.warn('⚠️ 可选JWT验证失败，继续执行:', (jwtError as Error).message);
          // 可选认证失败时不抛出错误，继续执行
        }
      }
    }
    
    next();
    
  } catch (error) {
    next(error);
  }
}

/**
 * 角色权限检查中间件
 * 需要在authenticateJWT之后使用
 */
export function requireRole(roles: string | string[]) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new AppError('用户未认证', 401);
      }
      
      const userRole = req.user.role;
      const allowedRoles = Array.isArray(roles) ? roles : [roles];
      
      if (!userRole || !allowedRoles.includes(userRole)) {
        throw new AppError('权限不足', 403);
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * 生成JWT token的工具函数
 */
export function generateJWT(payload: { id: string; email?: string; role?: string }): string {
  const jwtSecret = process.env.JWT_SECRET;
  const jwtExpiresIn = process.env.JWT_EXPIRES_IN || '7d';

  if (!jwtSecret) {
    throw new Error('JWT_SECRET环境变量未设置');
  }

  return jwt.sign(payload, jwtSecret, { expiresIn: jwtExpiresIn } as jwt.SignOptions);
}

/**
 * 验证JWT token的工具函数
 */
export function verifyJWT(token: string): JWTPayload {
  const jwtSecret = process.env.JWT_SECRET;
  
  if (!jwtSecret) {
    throw new Error('JWT_SECRET环境变量未设置');
  }
  
  return jwt.verify(token, jwtSecret) as JWTPayload;
}

// 导出类型定义供其他模块使用
export type { AuthenticatedRequest, JWTPayload };
