// 数据源类型
export type DataSourceType = 'postgresql' | 'mysql' | 'csv' | 'excel';

// 数据源状态
export type DataSourceStatus = 'connected' | 'disconnected' | 'error' | 'syncing';

// 关联关系类型
export type RelationshipType = 'one_to_one' | 'one_to_many' | 'many_to_one';

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 数据源列表项
export interface DataSourceListItem {
  id: string;
  name: string;
  type: DataSourceType;
  status: DataSourceStatus;
  createdAt: string;
  updatedAt: string;
}

// 数据表信息
export interface DataTableInfo {
  id: string;
  originalName: string;
  aliasName?: string;
  description?: string;
  columns: DataColumnInfo[];
}

// 数据列信息
export interface DataColumnInfo {
  id: string;
  originalName: string;
  aliasName?: string;
  dataType: string;
  description?: string;
  isPrimaryKey: boolean;
}

// 关联关系信息
export interface DataRelationshipInfo {
  id: string;
  fromColumnId: string;
  toColumnId: string;
  type: RelationshipType;
  isManual: boolean;
}

// 数据源Schema详情
export interface DataSourceSchema {
  id: string;
  name: string;
  type: DataSourceType;
  tables: DataTableInfo[];
  relationships: DataRelationshipInfo[];
}

// 数据库连接配置
export interface DatabaseConnectionConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
}

// 文件连接配置
export interface FileConnectionConfig {
  filename: string;
  path: string;
  encoding?: string;
  delimiter?: string; // for CSV
  sheetName?: string; // for Excel
}

export type ConnectionConfig = DatabaseConnectionConfig | FileConnectionConfig;

// 数据源创建请求
export interface CreateDataSourceRequest {
  name: string;
  type: DataSourceType;
  connectionConfig: ConnectionConfig;
}

// 聊天请求
export interface ChatRequest {
  sessionId: string;
  prompt: string;
}

// 聊天响应
export interface ChatResponse {
  id: string;
  sessionId: string;
  userPrompt: string;
  responseText: string;
  data?: {
    columns: string[];
    rows: any[][];
  };
  visualization?: {
    type: string;
    title: string;
    spec: any;
  };
  generatedSql?: string;
}

// 可视化图表类型
export type VisualizationType = 'bar_chart' | 'line_chart' | 'pie_chart' | 'table' | 'scatter_plot';

// ==================== 智能报表相关类型定义 ====================

// 用户角色类型
export type UserRole = 'analyst' | 'manager' | 'executive';

// 报表类型
export type ReportType = 'daily' | 'weekly' | 'monthly' | 'custom';

// 报表状态
export type ReportStatus = 'generating' | 'completed' | 'failed' | 'expired';

// 洞察类型
export type InsightType = 'anomaly' | 'trend' | 'opportunity' | 'risk';

// 洞察严重程度
export type InsightSeverity = 'low' | 'medium' | 'high' | 'critical';

// 洞察状态
export type InsightStatus = 'new' | 'acknowledged' | 'resolved';

// 用户配置接口
export interface UserProfile {
  id: string;
  userId: string;
  role: UserRole;
  preferences: {
    language?: string;
    timezone?: string;
    reportFormat?: string;
    notificationSettings?: {
      email?: boolean;
      push?: boolean;
    };
  };
  queryHistory?: {
    recentQueries: string[];
    favoriteTopics: string[];
    analysisPatterns: string[];
  };
  createdAt: string;
  updatedAt: string;
}

// 报表模板接口
export interface ReportTemplate {
  id: string;
  name: string;
  description?: string;
  config: {
    sections: ReportSection[];
    dataSourceIds: string[];
    refreshInterval?: number;
    exportFormats: string[];
  };
  userRole: UserRole;
  domain: string;
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// 报表章节接口
export interface ReportSection {
  id: string;
  title: string;
  type: 'summary' | 'chart' | 'table' | 'insight' | 'text';
  config: {
    query?: string;
    chartType?: string;
    filters?: any[];
    aggregations?: any[];
  };
  order: number;
}

// 报表实例接口
export interface ReportInstance {
  id: string;
  templateId: string;
  userId: string;
  title: string;
  content: {
    executiveSummary: string;
    sections: GeneratedReportSection[];
    keyInsights: DataInsight[];
    actionItems: string[];
    metadata: {
      generationTime: number;
      dataFreshness: string;
      confidence: number;
    };
  };
  metadata?: {
    generationDuration: number;
    dataSourcesUsed: string[];
    aiModelVersion: string;
  };
  status: ReportStatus;
  generatedAt: string;
  expiresAt?: string;
}

// 生成的报表章节
export interface GeneratedReportSection {
  id: string;
  title: string;
  type: string;
  content: {
    text?: string;
    data?: any;
    chart?: {
      type: string;
      spec: any;
    };
    insights?: string[];
  };
  order: number;
}

// 定时任务接口
export interface ScheduledTask {
  id: string;
  templateId: string;
  userId: string;
  scheduleType: ReportType;
  cronExpression: string;
  config: {
    recipients?: string[];
    exportFormat?: string;
    deliveryMethod?: 'email' | 'download' | 'api';
  };
  isActive: boolean;
  lastRun?: string;
  nextRun?: string;
  createdAt: string;
  updatedAt: string;
}

// 数据洞察接口
export interface DataInsight {
  id: string;
  dataSourceId: string;
  type: InsightType;
  title: string;
  description: string;
  confidence: number; // 0-1
  severity: InsightSeverity;
  status: InsightStatus;
  metadata?: {
    affectedMetrics?: string[];
    timeRange?: {
      start: string;
      end: string;
    };
    recommendations?: string[];
    relatedInsights?: string[];
  };
  createdAt: string;
}

// 报表生成请求
export interface ReportGenerationRequest {
  templateId?: string;
  userId: string;
  title?: string;
  config?: {
    dateRange?: {
      start: string;
      end: string;
    };
    dataSourceIds?: string[];
    customSections?: ReportSection[];
  };
  options?: {
    includeInsights?: boolean;
    generateCharts?: boolean;
    exportFormat?: string;
  };
}

// 报表生成响应
export interface ReportGenerationResponse {
  reportId: string;
  status: ReportStatus;
  estimatedCompletionTime?: number;
  downloadUrl?: string;
  previewUrl?: string;
}

// 智能建议接口
export interface IntelligentSuggestion {
  id: string;
  type: 'question' | 'analysis' | 'visualization' | 'insight';
  title: string;
  description: string;
  confidence: number;
  relevanceScore: number;
  metadata?: {
    dataSourceIds?: string[];
    suggestedActions?: string[];
    estimatedValue?: string;
  };
}

// 表单状态
export interface FormState<T> {
  data: T;
  errors: Partial<Record<keyof T, string>>;
  isSubmitting: boolean;
}

// 模态框状态
export interface ModalState {
  open: boolean;
  mode: 'create' | 'edit' | 'view';
  data?: any;
}

// 语义层更新请求
export interface UpdateTableAliasRequest {
  aliasName?: string;
  description?: string;
}

export interface UpdateColumnAliasRequest {
  aliasName?: string;
  description?: string;
}

export interface CreateRelationshipRequest {
  fromColumnId: string;
  toColumnId: string;
  relationshipType: RelationshipType;
}

// 关联关系发现结果
export interface RelationshipDiscoveryResult {
  id: string;
  fromTableName: string;
  fromColumnName: string;
  toTableName: string;
  toColumnName: string;
  relationshipType: RelationshipType;
  confidence: number;
  reasons: string[];
  isApplied: boolean;
  dataSourceId?: string;
  targetDataSourceId?: string;
}

// 关联关系发现配置
export interface RelationshipDiscoveryConfig {
  enableForeignKeyDetection: boolean;
  enableNamingConvention: boolean;
  enableDataAnalysis: boolean;
  enableCrossDataSource: boolean;
  confidenceThreshold: number;
  maxSuggestions: number;
  autoApply: boolean;
  autoApplyThreshold: number;
}

// 关联关系发现响应
export interface RelationshipDiscoveryResponse {
  discoveredCount: number;
  appliedCount: number;
  relationships: RelationshipDiscoveryResult[];
  statistics?: {
    highConfidence: number;
    mediumConfidence: number;
    lowConfidence: number;
    averageConfidence: number;
  };
  applyResults?: {
    applied: number;
    skipped: number;
    failed: number;
    results: any[];
  };
}

// 关联关系应用结果
export interface RelationshipApplyResult {
  applied: number;
  skipped: number;
  failed: number;
  results: Array<{
    relationship: RelationshipDiscoveryResult;
    status: 'applied' | 'skipped' | 'failed';
    error?: string;
  }>;
}
