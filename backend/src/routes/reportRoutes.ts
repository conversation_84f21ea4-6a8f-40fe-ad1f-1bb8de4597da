import { Router } from 'express';
import { ReportController } from '../controllers/reportController';
import { AIService } from '../services/aiService';
import { RedisClientWrapper } from '../utils/redisClient';
import { prisma } from '../lib/prisma';

/**
 * 智能报表路由
 * 定义所有与智能报表相关的API端点
 */

// 创建路由实例
const router = Router();

// 初始化依赖服务
const aiService = new AIService(prisma);
const redisClient = new RedisClientWrapper();

// 创建报表控制器实例
const reportController = new ReportController(aiService, redisClient);

// ==================== 报表生成相关路由 ====================

/**
 * 生成智能报表
 * POST /api/reports/generate
 * 
 * 请求体:
 * {
 *   "templateId": "string (可选)",
 *   "userId": "string (必需)",
 *   "title": "string (可选)",
 *   "config": {
 *     "dateRange": { "start": "string", "end": "string" },
 *     "dataSourceIds": ["string"],
 *     "customSections": [...]
 *   },
 *   "options": {
 *     "includeInsights": boolean,
 *     "generateCharts": boolean,
 *     "exportFormat": "string"
 *   }
 * }
 */
router.post('/generate', (req, res, next) => {
  reportController.generateReport(req, res, next);
});

/**
 * 获取报表详情
 * GET /api/reports/:reportId
 */
router.get('/:reportId', (req, res, next) => {
  reportController.getReport(req, res, next);
});

/**
 * 获取报表预览
 * GET /api/reports/:reportId/preview
 */
router.get('/:reportId/preview', (req, res, next) => {
  reportController.getReportPreview(req, res, next);
});

/**
 * 获取用户报表列表
 * GET /api/reports/user/:userId?limit=10
 */
router.get('/user/:userId', (req, res, next) => {
  reportController.getUserReports(req, res, next);
});

// ==================== 报表模板相关路由 ====================

/**
 * 获取预定义模板（需要放在其他模板路由之前，避免路径冲突）
 * GET /api/reports/templates/predefined
 */
router.get('/templates/predefined', (req, res, next) => {
  reportController.getPreDefinedTemplates(req, res, next);
});

/**
 * 初始化默认模板
 * POST /api/reports/templates/initialize
 */
router.post('/templates/initialize', (req, res, next) => {
  reportController.initializeDefaultTemplates(req, res, next);
});

/**
 * 创建报表模板
 * POST /api/reports/templates
 * 
 * 请求体:
 * {
 *   "name": "string (必需)",
 *   "description": "string (可选)",
 *   "config": {
 *     "sections": [...],
 *     "dataSourceIds": [...],
 *     "refreshInterval": number,
 *     "exportFormats": [...]
 *   },
 *   "userRole": "analyst|manager|executive",
 *   "domain": "string",
 *   "isPublic": boolean,
 *   "createdBy": "string"
 * }
 */
router.post('/templates', (req, res, next) => {
  reportController.createTemplate(req, res, next);
});

/**
 * 获取可用模板列表
 * GET /api/reports/templates?userRole=analyst&userId=xxx
 */
router.get('/templates', (req, res, next) => {
  reportController.getAvailableTemplates(req, res, next);
});

/**
 * 获取模板详情
 * GET /api/reports/templates/:templateId
 */
router.get('/templates/:templateId', (req, res, next) => {
  reportController.getTemplate(req, res, next);
});

/**
 * 更新报表模板
 * PUT /api/reports/templates/:templateId
 * 
 * 请求体: 部分模板字段
 */
router.put('/templates/:templateId', (req, res, next) => {
  reportController.updateTemplate(req, res, next);
});

/**
 * 删除报表模板
 * DELETE /api/reports/templates/:templateId
 */
router.delete('/templates/:templateId', (req, res, next) => {
  reportController.deleteTemplate(req, res, next);
});

/**
 * 复制报表模板
 * POST /api/reports/templates/:templateId/clone
 * 
 * 请求体:
 * {
 *   "newName": "string",
 *   "createdBy": "string"
 * }
 */
router.post('/templates/:templateId/clone', (req, res, next) => {
  reportController.cloneTemplate(req, res, next);
});

// ==================== 报表导出相关路由 ====================

/**
 * 导出报表为PDF
 * GET /api/reports/:reportId/export/pdf
 */
router.get('/:reportId/export/pdf', async (req, res, next) => {
  try {
    // TODO: 实现PDF导出功能
    res.json({
      success: false,
      message: 'PDF导出功能正在开发中'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 导出报表为Excel
 * GET /api/reports/:reportId/export/excel
 */
router.get('/:reportId/export/excel', async (req, res, next) => {
  try {
    // TODO: 实现Excel导出功能
    res.json({
      success: false,
      message: 'Excel导出功能正在开发中'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 导出报表为HTML
 * GET /api/reports/:reportId/export/html
 */
router.get('/:reportId/export/html', async (req, res, next) => {
  try {
    // TODO: 实现HTML导出功能
    res.json({
      success: false,
      message: 'HTML导出功能正在开发中'
    });
  } catch (error) {
    next(error);
  }
});

// ==================== 报表统计相关路由 ====================

/**
 * 获取报表生成统计
 * GET /api/reports/stats/generation
 */
router.get('/stats/generation', async (req, res, next) => {
  try {
    // TODO: 实现报表生成统计功能
    res.json({
      success: true,
      data: {
        totalReports: 0,
        todayReports: 0,
        successRate: 0,
        avgGenerationTime: 0
      },
      message: '报表统计功能正在开发中'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取模板使用统计
 * GET /api/reports/stats/templates
 */
router.get('/stats/templates', async (req, res, next) => {
  try {
    // TODO: 实现模板使用统计功能
    res.json({
      success: true,
      data: {
        totalTemplates: 0,
        publicTemplates: 0,
        mostUsedTemplate: null,
        templateUsageRanking: []
      },
      message: '模板统计功能正在开发中'
    });
  } catch (error) {
    next(error);
  }
});

// ==================== 错误处理 ====================

/**
 * 路由级错误处理中间件
 */
router.use((error: any, req: any, res: any, next: any) => {
  console.error('报表路由错误:', error);
  
  // 如果响应已经发送，则传递给下一个错误处理器
  if (res.headersSent) {
    return next(error);
  }

  // 根据错误类型返回相应的状态码和消息
  const statusCode = error.statusCode || 500;
  const message = error.message || '内部服务器错误';

  res.status(statusCode).json({
    success: false,
    error: message,
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

export default router;
