import { 
  LLMChatRequest, 
  LLMChatResponse, 
  LLMClientConfig, 
  LLMModelType, 
  LLMUsage,
  LLMServiceError 
} from '../../types';

/**
 * 增强版LLM客户端
 * 专门用于通用聊天功能，支持多种模型和提供商
 */
export class EnhancedLLMClient {
  private config: LLMClientConfig;
  private tokenUsage: { total: number; daily: number } = { total: 0, daily: 0 };
  private retryCount: number = 3;
  private retryDelay: number = 1000; // 1秒

  constructor(config: LLMClientConfig) {
    this.config = config;
  }

  /**
   * 发送聊天请求（带重试机制）
   */
  async chat(request: LLMChatRequest): Promise<LLMChatResponse> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.retryCount; attempt++) {
      try {
        console.log(`🤖 LLM请求尝试 ${attempt}/${this.retryCount}，模型: ${request.model}`);
        
        // 检查token限制
        const estimatedTokens = this.estimateTokens(
          request.messages.map(m => m.content).join(' ')
        );
        
        if (!(await this.checkTokenLimit(estimatedTokens))) {
          throw new LLMServiceError('每日token使用量已达上限', 429);
        }

        const response = await this.executeChat(request);
        
        // 记录成功的请求
        console.log(`✅ LLM请求成功，使用token: ${response.data.usage.total_tokens}`);
        
        return response;
      } catch (error) {
        lastError = error as Error;
        console.error(`❌ LLM请求失败 (尝试 ${attempt}/${this.retryCount}):`, error);
        
        // 如果是最后一次尝试或者是不可重试的错误，直接抛出
        if (attempt === this.retryCount || !this.isRetryableError(error as Error)) {
          break;
        }
        
        // 等待后重试
        await this.delay(this.retryDelay * attempt);
      }
    }
    
    throw new LLMServiceError(
      `LLM请求失败，已重试${this.retryCount}次: ${lastError?.message}`,
      500
    );
  }

  /**
   * 执行具体的聊天请求
   */
  private async executeChat(request: LLMChatRequest): Promise<LLMChatResponse> {
    const requestId = this.generateRequestId();
    const timestamp = Date.now();
    
    try {
      let apiResponse: any;
      let usage: LLMUsage;
      let content: string;
      
      switch (this.config.provider) {
        case 'openai':
          ({ content, usage } = await this.chatWithOpenAI(request));
          break;
        case 'deepseek':
          ({ content, usage } = await this.chatWithDeepSeek(request));
          break;
        case 'anthropic':
          ({ content, usage } = await this.chatWithAnthropic(request));
          break;
        case 'azure':
          ({ content, usage } = await this.chatWithAzure(request));
          break;
        case 'local':
          ({ content, usage } = await this.chatWithLocal(request));
          break;
        default:
          throw new LLMServiceError(`不支持的LLM提供商: ${this.config.provider}`, 400);
      }

      // 记录token使用量
      this.recordTokenUsage(usage);

      const messageId = this.generateMessageId();
      
      return {
        success: true,
        data: {
          message: {
            role: 'assistant',
            content,
            id: messageId,
            timestamp
          },
          conversation_id: request.conversation_id || this.generateConversationId(),
          message_id: messageId,
          parent_message_id: undefined, // 将在服务层设置
          request_id: requestId,
          model: request.model,
          finish_reason: 'stop', // 默认值，实际应从API响应中获取
          usage
        },
        requestId,
        timestamp
      };
    } catch (error) {
      console.error('LLM API调用失败:', error);
      throw error;
    }
  }

  /**
   * OpenAI API调用
   */
  private async chatWithOpenAI(request: LLMChatRequest): Promise<{ content: string; usage: LLMUsage }> {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new LLMServiceError('OPENAI_API_KEY环境变量未设置', 500);
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: request.model,
        messages: request.messages,
        temperature: request.options?.temperature || this.config.temperature || 0.7,
        max_tokens: request.options?.max_tokens || this.config.maxTokens || 2000,
        stream: false
      }),
      signal: AbortSignal.timeout(this.config.timeout || 30000)
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new LLMServiceError(
        `OpenAI API错误: ${error.error?.message || response.statusText}`,
        response.status
      );
    }

    const data = await response.json();
    
    return {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage || { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
    };
  }

  /**
   * DeepSeek API调用
   */
  private async chatWithDeepSeek(request: LLMChatRequest): Promise<{ content: string; usage: LLMUsage }> {
    const apiKey = process.env.DEEPSEEK_API_KEY;
    if (!apiKey) {
      throw new LLMServiceError('DEEPSEEK_API_KEY环境变量未设置', 500);
    }

    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: request.model,
        messages: request.messages,
        temperature: request.options?.temperature || this.config.temperature || 0.7,
        max_tokens: request.options?.max_tokens || this.config.maxTokens || 2000,
        stream: false
      }),
      signal: AbortSignal.timeout(this.config.timeout || 30000)
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new LLMServiceError(
        `DeepSeek API错误: ${error.error?.message || response.statusText}`,
        response.status
      );
    }

    const data = await response.json();
    
    return {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage || { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
    };
  }

  /**
   * Anthropic Claude API调用
   */
  private async chatWithAnthropic(request: LLMChatRequest): Promise<{ content: string; usage: LLMUsage }> {
    const apiKey = process.env.ANTHROPIC_API_KEY;
    if (!apiKey) {
      throw new LLMServiceError('ANTHROPIC_API_KEY环境变量未设置', 500);
    }

    // 转换消息格式
    const messages = request.messages.filter(m => m.role !== 'system');
    const systemMessage = request.messages.find(m => m.role === 'system')?.content;

    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: request.model,
        max_tokens: request.options?.max_tokens || this.config.maxTokens || 2000,
        temperature: request.options?.temperature || this.config.temperature || 0.7,
        system: systemMessage,
        messages: messages.map(m => ({
          role: m.role === 'assistant' ? 'assistant' : 'user',
          content: m.content
        }))
      }),
      signal: AbortSignal.timeout(this.config.timeout || 30000)
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new LLMServiceError(
        `Anthropic API错误: ${error.error?.message || response.statusText}`,
        response.status
      );
    }

    const data = await response.json();
    const usage = data.usage;
    
    return {
      content: data.content[0]?.text || '',
      usage: usage ? {
        prompt_tokens: usage.input_tokens,
        completion_tokens: usage.output_tokens,
        total_tokens: usage.input_tokens + usage.output_tokens
      } : { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
    };
  }

  /**
   * Azure OpenAI API调用
   */
  private async chatWithAzure(request: LLMChatRequest): Promise<{ content: string; usage: LLMUsage }> {
    const apiKey = process.env.AZURE_OPENAI_API_KEY;
    const endpoint = process.env.AZURE_OPENAI_ENDPOINT;
    const deploymentName = process.env.AZURE_OPENAI_DEPLOYMENT_NAME;

    if (!apiKey || !endpoint || !deploymentName) {
      throw new LLMServiceError('Azure OpenAI环境变量未完整设置', 500);
    }

    const url = `${endpoint}/openai/deployments/${deploymentName}/chat/completions?api-version=2024-02-15-preview`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'api-key': apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        messages: request.messages,
        temperature: request.options?.temperature || this.config.temperature || 0.7,
        max_tokens: request.options?.max_tokens || this.config.maxTokens || 2000,
        stream: false
      }),
      signal: AbortSignal.timeout(this.config.timeout || 30000)
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new LLMServiceError(
        `Azure OpenAI API错误: ${error.error?.message || response.statusText}`,
        response.status
      );
    }

    const data = await response.json();
    
    return {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage || { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
    };
  }

  /**
   * 本地模型API调用
   */
  private async chatWithLocal(request: LLMChatRequest): Promise<{ content: string; usage: LLMUsage }> {
    const endpoint = this.config.baseUrl || process.env.LOCAL_LLM_ENDPOINT || 'http://localhost:8000';
    
    const response = await fetch(`${endpoint}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: request.model,
        messages: request.messages,
        temperature: request.options?.temperature || this.config.temperature || 0.7,
        max_tokens: request.options?.max_tokens || this.config.maxTokens || 2000,
        stream: false
      }),
      signal: AbortSignal.timeout(this.config.timeout || 30000)
    });

    if (!response.ok) {
      throw new LLMServiceError(`本地LLM API错误: ${response.statusText}`, response.status);
    }

    const data = await response.json();
    
    return {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage || { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
    };
  }

  /**
   * 工具方法：检查是否为可重试的错误
   */
  private isRetryableError(error: Error): boolean {
    const retryableMessages = [
      'timeout',
      'network',
      'connection',
      'rate limit',
      '429',
      '500',
      '502',
      '503',
      '504'
    ];

    const errorMessage = error.message.toLowerCase();
    return retryableMessages.some(msg => errorMessage.includes(msg));
  }

  /**
   * 工具方法：延迟执行
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 工具方法：记录token使用量
   */
  private recordTokenUsage(usage: LLMUsage): void {
    this.tokenUsage.total += usage.total_tokens;
    this.tokenUsage.daily += usage.total_tokens;
  }

  /**
   * 工具方法：生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 工具方法：生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 工具方法：生成对话ID
   */
  private generateConversationId(): string {
    return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 检查token使用限制
   */
  async checkTokenLimit(estimatedTokens: number): Promise<boolean> {
    const dailyLimit = parseInt(process.env.LLM_DAILY_TOKEN_LIMIT || '100000');
    return this.tokenUsage.daily + estimatedTokens <= dailyLimit;
  }

  /**
   * 获取token使用统计
   */
  getTokenUsage(): { total: number; daily: number } {
    return { ...this.tokenUsage };
  }

  /**
   * 重置每日token使用量
   */
  resetDailyTokenUsage(): void {
    this.tokenUsage.daily = 0;
  }

  /**
   * 估算prompt的token数量
   */
  estimateTokens(text: string): number {
    // 简单估算：英文约4字符=1token，中文约1.5字符=1token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const otherChars = text.length - chineseChars;
    return Math.ceil(chineseChars / 1.5 + otherChars / 4);
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.chat({
        messages: [{ role: 'user', content: 'Hello' }],
        model: this.config.model,
        stream: false
      });
      return response.success && response.data.message.content.length > 0;
    } catch (error) {
      console.error('LLM健康检查失败:', error);
      return false;
    }
  }

  /**
   * 获取支持的模型列表
   */
  static getSupportedModels(): LLMModelType[] {
    return ['deepseek', 'gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'claude-3', 'local'];
  }

  /**
   * 创建默认配置
   */
  static createDefaultConfig(provider: string, model: LLMModelType): LLMClientConfig {
    return {
      provider: provider as any,
      model,
      temperature: 0.7,
      maxTokens: 2000,
      timeout: 30000
    };
  }
}
