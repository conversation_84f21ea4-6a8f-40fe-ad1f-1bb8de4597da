"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('开始数据库种子数据初始化...');
    // 创建示例数据源
    const sampleDataSource = await prisma.dataSource.create({
        data: {
            name: '示例PostgreSQL数据库',
            type: 'postgresql',
            connectionConfig: JSON.stringify({
                host: 'localhost',
                port: 5432,
                database: 'sample_db',
                username: 'sample_user',
                // 注意：在实际应用中，密码应该被加密
                password: 'encrypted_password_here'
            })
        }
    });
    // 创建示例数据表
    const customersTable = await prisma.dataTable.create({
        data: {
            dataSourceId: sampleDataSource.id,
            originalTableName: 'customers',
            aliasName: '客户表',
            description: '存储客户基本信息的表',
            isSynced: true
        }
    });
    const ordersTable = await prisma.dataTable.create({
        data: {
            dataSourceId: sampleDataSource.id,
            originalTableName: 'orders',
            aliasName: '订单表',
            description: '存储订单信息的表',
            isSynced: true
        }
    });
    // 创建示例数据列 - 客户表
    const customerIdColumn = await prisma.dataColumn.create({
        data: {
            dataTableId: customersTable.id,
            originalColumnName: 'id',
            originalDataType: 'integer',
            aliasName: '客户ID',
            description: '客户的唯一标识符',
            isPrimaryKey: true
        }
    });
    const customerNameColumn = await prisma.dataColumn.create({
        data: {
            dataTableId: customersTable.id,
            originalColumnName: 'customer_name',
            originalDataType: 'varchar',
            aliasName: '客户姓名',
            description: '客户的姓名'
        }
    });
    const customerEmailColumn = await prisma.dataColumn.create({
        data: {
            dataTableId: customersTable.id,
            originalColumnName: 'email',
            originalDataType: 'varchar',
            aliasName: '客户邮箱',
            description: '客户的电子邮箱地址'
        }
    });
    // 创建示例数据列 - 订单表
    const orderIdColumn = await prisma.dataColumn.create({
        data: {
            dataTableId: ordersTable.id,
            originalColumnName: 'id',
            originalDataType: 'integer',
            aliasName: '订单ID',
            description: '订单的唯一标识符',
            isPrimaryKey: true
        }
    });
    const orderCustomerIdColumn = await prisma.dataColumn.create({
        data: {
            dataTableId: ordersTable.id,
            originalColumnName: 'customer_id',
            originalDataType: 'integer',
            aliasName: '客户ID',
            description: '关联的客户ID'
        }
    });
    const orderAmountColumn = await prisma.dataColumn.create({
        data: {
            dataTableId: ordersTable.id,
            originalColumnName: 'amount',
            originalDataType: 'decimal',
            aliasName: '订单金额',
            description: '订单的总金额'
        }
    });
    // 创建示例关联关系
    await prisma.dataRelationship.create({
        data: {
            fromColumnId: customerIdColumn.id,
            toColumnId: orderCustomerIdColumn.id,
            relationshipType: 'one_to_many',
            isManual: false
        }
    });
    console.log('数据库种子数据初始化完成！');
}
main()
    .catch((e) => {
    console.error('种子数据初始化失败:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map