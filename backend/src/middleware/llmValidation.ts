import { Request, Response, NextFunction } from 'express';
import { LLMServiceError } from '../types';

/**
 * LLM聊天请求验证中间件
 */
export function validateLLMChatRequest(req: Request, res: Response, next: NextFunction) {
  try {
    const { body } = req;

    // 基础字段验证
    validateRequiredFields(body);
    validateMessages(body.messages);
    validateModel(body.model);
    validateStream(body.stream);
    
    // 可选字段验证
    if (body.conversation_id) {
      validateConversationId(body.conversation_id);
    }
    
    if (body.options) {
      validateOptions(body.options);
    }

    // 安全过滤
    sanitizeRequest(body);
    
    // 记录请求日志
    logRequest(req);

    next();
  } catch (error) {
    next(error);
  }
}

/**
 * 验证必需字段
 */
function validateRequiredFields(body: any): void {
  if (!body) {
    throw new LLMServiceError('请求体不能为空', 400);
  }

  const requiredFields = ['messages', 'model', 'stream'];
  for (const field of requiredFields) {
    if (!(field in body)) {
      throw new LLMServiceError(`缺少必需字段: ${field}`, 400);
    }
  }
}

/**
 * 验证消息数组
 */
function validateMessages(messages: any): void {
  if (!Array.isArray(messages)) {
    throw new LLMServiceError('messages字段必须是数组', 400);
  }

  if (messages.length === 0) {
    throw new LLMServiceError('messages数组不能为空', 400);
  }

  if (messages.length > 50) {
    throw new LLMServiceError('单次请求消息数量不能超过50条', 400);
  }

  // 验证每条消息
  messages.forEach((message: any, index: number) => {
    validateSingleMessage(message, index);
  });

  // 验证最后一条消息必须是用户消息
  const lastMessage = messages[messages.length - 1];
  if (lastMessage.role !== 'user') {
    throw new LLMServiceError('最后一条消息必须是用户消息', 400);
  }
}

/**
 * 验证单条消息
 */
function validateSingleMessage(message: any, index: number): void {
  if (!message || typeof message !== 'object') {
    throw new LLMServiceError(`消息${index + 1}必须是对象`, 400);
  }

  // 验证role字段
  if (!message.role || typeof message.role !== 'string') {
    throw new LLMServiceError(`消息${index + 1}缺少有效的role字段`, 400);
  }

  const validRoles = ['user', 'assistant', 'system'];
  if (!validRoles.includes(message.role)) {
    throw new LLMServiceError(`消息${index + 1}的role字段无效: ${message.role}`, 400);
  }

  // 验证content字段
  if (!message.content || typeof message.content !== 'string') {
    throw new LLMServiceError(`消息${index + 1}缺少有效的content字段`, 400);
  }

  if (message.content.trim().length === 0) {
    throw new LLMServiceError(`消息${index + 1}的content不能为空`, 400);
  }

  if (message.content.length > 10000) {
    throw new LLMServiceError(`消息${index + 1}的content长度不能超过10000字符`, 400);
  }
}

/**
 * 验证模型字段
 */
function validateModel(model: any): void {
  if (!model || typeof model !== 'string') {
    throw new LLMServiceError('model字段必须是有效的字符串', 400);
  }

  const supportedModels = [
    'deepseek',
    'gpt-3.5-turbo',
    'gpt-4',
    'gpt-4-turbo',
    'claude-3',
    'local'
  ];

  if (!supportedModels.includes(model)) {
    throw new LLMServiceError(`不支持的模型: ${model}`, 400);
  }
}

/**
 * 验证stream字段
 */
function validateStream(stream: any): void {
  if (typeof stream !== 'boolean') {
    throw new LLMServiceError('stream字段必须是布尔值', 400);
  }

  // 目前只支持非流式
  if (stream === true) {
    throw new LLMServiceError('暂不支持流式响应', 400);
  }
}

/**
 * 验证对话ID
 */
function validateConversationId(conversationId: any): void {
  if (typeof conversationId !== 'string') {
    throw new LLMServiceError('conversation_id必须是字符串', 400);
  }

  if (conversationId.length === 0 || conversationId.length > 100) {
    throw new LLMServiceError('conversation_id长度必须在1-100字符之间', 400);
  }

  // 验证格式（只允许字母、数字、下划线、连字符）
  const validPattern = /^[a-zA-Z0-9_-]+$/;
  if (!validPattern.test(conversationId)) {
    throw new LLMServiceError('conversation_id格式无效', 400);
  }
}

/**
 * 验证选项字段
 */
function validateOptions(options: any): void {
  if (typeof options !== 'object' || options === null) {
    throw new LLMServiceError('options字段必须是对象', 400);
  }

  // 验证temperature
  if (options.temperature !== undefined) {
    const temp = parseFloat(options.temperature);
    if (isNaN(temp) || temp < 0 || temp > 2) {
      throw new LLMServiceError('temperature必须是0-2之间的数值', 400);
    }
  }

  // 验证max_tokens
  if (options.max_tokens !== undefined) {
    const maxTokens = parseInt(options.max_tokens);
    if (isNaN(maxTokens) || maxTokens < 1 || maxTokens > 8000) {
      throw new LLMServiceError('max_tokens必须是1-8000之间的整数', 400);
    }
  }

  // 验证deep_thinking
  if (options.deep_thinking !== undefined && typeof options.deep_thinking !== 'boolean') {
    throw new LLMServiceError('deep_thinking字段必须是布尔值', 400);
  }

  // 验证online_search
  if (options.online_search !== undefined && typeof options.online_search !== 'boolean') {
    throw new LLMServiceError('online_search字段必须是布尔值', 400);
  }
}

/**
 * 安全过滤请求内容
 */
function sanitizeRequest(body: any): void {
  // 过滤敏感内容
  const sensitivePatterns = [
    /password\s*[:=]\s*\S+/gi,
    /api[_-]?key\s*[:=]\s*\S+/gi,
    /token\s*[:=]\s*\S+/gi,
    /secret\s*[:=]\s*\S+/gi
  ];

  body.messages.forEach((message: any) => {
    sensitivePatterns.forEach(pattern => {
      if (pattern.test(message.content)) {
        console.warn('⚠️ 检测到可能的敏感信息，已进行脱敏处理');
        message.content = message.content.replace(pattern, '[REDACTED]');
      }
    });

    // 移除多余的空白字符
    message.content = message.content.trim().replace(/\s+/g, ' ');
  });
}

/**
 * 记录请求日志
 */
function logRequest(req: Request): void {
  const logData = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip || req.connection.remoteAddress,
    model: req.body.model,
    messageCount: req.body.messages?.length,
    conversationId: req.body.conversation_id,
    options: req.body.options
  };

  console.log('📝 LLM聊天请求日志:', JSON.stringify(logData, null, 2));
}

/**
 * 速率限制中间件
 */
const requestCounts = new Map<string, { count: number; resetTime: number }>();

export function rateLimitMiddleware(req: Request, res: Response, next: NextFunction) {
  const clientId = req.ip || 'unknown';
  const now = Date.now();
  const windowMs = 60 * 1000; // 1分钟窗口
  const maxRequests = 30; // 每分钟最多30次请求

  const clientData = requestCounts.get(clientId);
  
  if (!clientData || now > clientData.resetTime) {
    // 重置或初始化计数
    requestCounts.set(clientId, {
      count: 1,
      resetTime: now + windowMs
    });
    next();
  } else if (clientData.count < maxRequests) {
    // 增加计数
    clientData.count++;
    next();
  } else {
    // 超过限制
    console.warn(`⚠️ 客户端 ${clientId} 超过速率限制`);
    const error = new LLMServiceError('请求过于频繁，请稍后再试', 429);
    next(error);
  }
}

/**
 * 内容安全检查中间件
 */
export function contentSecurityMiddleware(req: Request, res: Response, next: NextFunction) {
  try {
    const { messages } = req.body;
    
    if (!messages || !Array.isArray(messages)) {
      return next();
    }

    // 检查是否包含不当内容
    const inappropriatePatterns = [
      /\b(hack|crack|exploit|vulnerability)\b/gi,
      /\b(bomb|weapon|violence|kill)\b/gi,
      /\b(illegal|fraud|scam|phishing)\b/gi
    ];

    for (const message of messages) {
      if (message.content) {
        for (const pattern of inappropriatePatterns) {
          if (pattern.test(message.content)) {
            console.warn('⚠️ 检测到可能的不当内容');
            throw new LLMServiceError('请求内容包含不当信息，请修改后重试', 400);
          }
        }
      }
    }

    next();
  } catch (error) {
    next(error);
  }
}
