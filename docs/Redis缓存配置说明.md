# Redis缓存配置说明

## 概述

本系统实现了智能的Redis缓存机制，支持条件性使用Redis缓存。当Redis不可用时，系统会优雅降级，直接从数据库获取数据，确保应用程序的稳定运行。

## 功能特性

### 1. 条件性启用
- 基于环境变量自动检测是否启用Redis
- 支持完全禁用Redis缓存功能
- 无需修改代码即可切换缓存策略

### 2. 优雅降级
- Redis连接失败时自动降级
- 缓存操作失败时不影响主要功能
- 详细的日志记录和状态监控

### 3. 智能重连
- 自动处理连接断开情况
- 支持健康检查和状态监控
- 连接状态实时反馈

## 环境变量配置

### 启用Redis缓存

```bash
# Redis服务器连接URL
REDIS_URL="redis://localhost:6379"

# 带认证的Redis连接
REDIS_URL="redis://username:password@localhost:6379"

# Redis Cluster连接
REDIS_URL="redis://localhost:6379,localhost:6380,localhost:6381"
```

### 禁用Redis缓存

```bash
# 方法1：设置为空字符串
REDIS_URL=""

# 方法2：注释掉配置项
# REDIS_URL="redis://localhost:6379"

# 方法3：删除环境变量
# unset REDIS_URL
```

## 使用方法

### 1. 基本缓存操作

```typescript
import { redisClient } from '../utils/redisClient';

// 获取缓存
const cachedData = await redisClient.get('cache-key');
if (cachedData) {
  return JSON.parse(cachedData);
}

// 设置缓存（永久）
const data = { id: 1, name: 'example' };
await redisClient.set('cache-key', JSON.stringify(data));

// 设置缓存（带过期时间）
await redisClient.set('cache-key', JSON.stringify(data), 3600); // 1小时

// 删除缓存
await redisClient.del('cache-key');

// 检查键是否存在
const exists = await redisClient.exists('cache-key');

// 设置过期时间
await redisClient.expire('cache-key', 1800); // 30分钟
```

### 2. 缓存状态检查

```typescript
import { redisClient } from '../utils/redisClient';

// 检查Redis是否可用
if (redisClient.isAvailable()) {
  console.log('Redis缓存可用');
} else {
  console.log('Redis缓存不可用，使用直接数据访问');
}

// 获取详细状态信息
const status = redisClient.getStatus();
console.log('Redis状态:', status);
/*
输出示例：
{
  enabled: true,
  connected: true,
  available: true,
  redisUrl: "redis://localhost:6379"
}
*/

// 健康检查
const isHealthy = await redisClient.healthCheck();
console.log('Redis健康状态:', isHealthy);
```

### 3. 在服务中使用缓存

```typescript
export class DataService {
  async getData(id: string): Promise<any> {
    // 尝试从缓存获取
    const cacheKey = `data:${id}`;
    const cachedData = await redisClient.get(cacheKey);
    
    if (cachedData) {
      console.log('✅ 缓存命中');
      return JSON.parse(cachedData);
    }
    
    // 从数据库获取
    console.log('🔄 缓存未命中，从数据库获取');
    const data = await this.fetchFromDatabase(id);
    
    // 缓存数据（如果Redis可用）
    await redisClient.set(cacheKey, JSON.stringify(data), 3600);
    
    return data;
  }
  
  async updateData(id: string, data: any): Promise<void> {
    // 更新数据库
    await this.updateDatabase(id, data);
    
    // 清除相关缓存
    await redisClient.del(`data:${id}`);
    
    console.log('✅ 数据已更新，缓存已清除');
  }
}
```

## 配置示例

### 开发环境配置

```bash
# .env.development
# 启用Redis缓存
REDIS_URL="redis://localhost:6379"

# 或者禁用Redis缓存进行测试
# REDIS_URL=""
```

### 生产环境配置

```bash
# .env.production
# 生产Redis服务器
REDIS_URL="redis://prod-redis-server:6379"

# 带认证的生产环境
REDIS_URL="redis://username:password@prod-redis-server:6379/0"

# Redis Sentinel配置
REDIS_URL="redis-sentinel://sentinel1:26379,sentinel2:26379,sentinel3:26379/mymaster"
```

### Docker环境配置

```yaml
# docker-compose.yml
version: '3.8'
services:
  backend:
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

## 日志记录

系统会记录以下Redis相关日志：

### 连接状态日志
```
📝 Redis已配置：将尝试连接Redis服务器
✅ Redis连接成功
❌ Redis连接失败，将使用无缓存模式: Connection refused
📝 Redis未配置，将使用无缓存模式
```

### 操作日志
```
✅ Redis缓存命中: user:123
❌ Redis缓存未命中: user:123
✅ Redis缓存写入成功: user:123
❌ Redis写入失败，key: user:123, 错误: Connection lost
🔄 Redis不可用，跳过缓存读取: user:123
```

## 性能优化建议

### 1. 缓存策略
- 为不同类型的数据设置合适的TTL
- 使用有意义的缓存键命名规范
- 避免缓存过大的数据对象

### 2. 连接优化
- 使用连接池提高性能
- 设置合适的超时时间
- 监控连接状态和性能指标

### 3. 内存管理
```bash
# Redis配置优化
maxmemory 256mb
maxmemory-policy allkeys-lru
```

## 故障排除

### 常见问题

1. **Redis连接失败**
   ```
   ❌ Redis连接错误: ECONNREFUSED
   ```
   - 检查Redis服务是否运行
   - 验证REDIS_URL配置正确
   - 检查网络连接和防火墙设置

2. **缓存操作失败**
   ```
   ❌ Redis读取失败，key: test, 错误: Connection lost
   ```
   - 检查Redis服务状态
   - 验证网络连接稳定性
   - 查看Redis服务器日志

3. **内存不足**
   ```
   ❌ Redis写入失败: OOM command not allowed
   ```
   - 检查Redis内存使用情况
   - 调整maxmemory配置
   - 清理过期或不需要的缓存

### 调试技巧

1. **启用详细日志**
   ```typescript
   // 检查Redis状态
   console.log('Redis状态:', redisClient.getStatus());
   
   // 执行健康检查
   const healthy = await redisClient.healthCheck();
   console.log('健康检查结果:', healthy);
   ```

2. **监控缓存命中率**
   ```typescript
   let cacheHits = 0;
   let cacheMisses = 0;
   
   const data = await redisClient.get(key);
   if (data) {
     cacheHits++;
   } else {
     cacheMisses++;
   }
   
   console.log(`缓存命中率: ${(cacheHits / (cacheHits + cacheMisses) * 100).toFixed(2)}%`);
   ```

3. **测试缓存功能**
   ```bash
   # 直接测试Redis连接
   redis-cli -h localhost -p 6379 ping
   
   # 查看Redis信息
   redis-cli -h localhost -p 6379 info
   
   # 监控Redis操作
   redis-cli -h localhost -p 6379 monitor
   ```

## 最佳实践

1. **缓存键设计**
   - 使用有意义的前缀：`user:123`, `session:abc`
   - 避免键名冲突
   - 保持键名简洁但具有描述性

2. **数据序列化**
   - 使用JSON.stringify/parse处理对象
   - 考虑使用更高效的序列化方案（如MessagePack）

3. **错误处理**
   - 始终处理缓存操作可能的异常
   - 缓存失败不应影响主要业务逻辑
   - 提供降级方案

4. **监控和告警**
   - 监控Redis连接状态
   - 设置缓存命中率告警
   - 监控内存使用情况
