# JWT认证配置说明

## 概述

本系统实现了灵活的JWT认证机制，支持开发模式下跳过认证验证，便于开发和测试。同时在生产环境中提供完整的JWT token验证功能。

## 功能特性

### 1. 开发模式支持
- 支持在开发环境下跳过JWT验证
- 提供两种跳过认证的配置方式
- 自动为开发模式请求添加默认用户信息

### 2. 生产环境安全
- 完整的JWT token验证
- 支持token过期检查
- 详细的错误信息和日志记录

### 3. 灵活的中间件
- 必需认证中间件 (`authenticateJWT`)
- 可选认证中间件 (`optionalAuthenticateJWT`)
- 角色权限检查中间件 (`requireRole`)

## 环境变量配置

### 基础JWT配置

```bash
# JWT签名密钥（必需）
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# JWT token过期时间（可选，默认7天）
JWT_EXPIRES_IN=7d
```

### 开发模式配置

```bash
# 开发模式：设置为 'true' 时在开发环境下跳过JWT验证
DEV_MODE=false

# 强制跳过JWT验证：设置为 'true' 时在任何环境下都跳过JWT验证
# 警告：生产环境中请勿设置为 true
SKIP_JWT_AUTH=false

# 当前运行环境
NODE_ENV=development
```

## 跳过认证的条件

系统会在以下情况下跳过JWT验证：

1. **SKIP_JWT_AUTH=true**：强制跳过（任何环境）
2. **DEV_MODE=true 且 NODE_ENV=development**：开发模式跳过

优先级：`SKIP_JWT_AUTH` > `DEV_MODE + NODE_ENV`

## 使用方法

### 1. 路由中使用认证中间件

```typescript
import { authenticateJWT, optionalAuthenticateJWT, requireRole } from '../middleware/auth';

// 必需认证的路由
router.get('/protected', authenticateJWT, controller);

// 可选认证的路由
router.get('/optional', optionalAuthenticateJWT, controller);

// 需要特定角色的路由
router.post('/admin', authenticateJWT, requireRole('admin'), controller);

// 需要多个角色之一的路由
router.put('/editor', authenticateJWT, requireRole(['admin', 'editor']), controller);
```

### 2. 在控制器中获取用户信息

```typescript
import { AuthenticatedRequest } from '../middleware/auth';

export async function protectedController(req: AuthenticatedRequest, res: Response) {
  // 获取当前用户信息
  const userId = req.user?.id;
  const userEmail = req.user?.email;
  const userRole = req.user?.role;
  
  // 业务逻辑...
}
```

### 3. 生成和验证JWT token

```typescript
import { generateJWT, verifyJWT } from '../middleware/auth';

// 生成JWT token
const token = generateJWT({
  id: 'user123',
  email: '<EMAIL>',
  role: 'user'
});

// 验证JWT token
try {
  const payload = verifyJWT(token);
  console.log('用户ID:', payload.id);
} catch (error) {
  console.error('Token验证失败:', error.message);
}
```

## 开发环境配置示例

### 开发时跳过认证

```bash
# .env.development
NODE_ENV=development
DEV_MODE=true
SKIP_JWT_AUTH=false
JWT_SECRET=dev-secret-key
```

### 开发时启用认证

```bash
# .env.development
NODE_ENV=development
DEV_MODE=false
SKIP_JWT_AUTH=false
JWT_SECRET=dev-secret-key
```

## 生产环境配置示例

```bash
# .env.production
NODE_ENV=production
DEV_MODE=false
SKIP_JWT_AUTH=false
JWT_SECRET=your-super-secure-production-secret-key
JWT_EXPIRES_IN=24h
```

## 错误处理

系统会返回以下HTTP状态码和错误信息：

- **401 Unauthorized**：
  - 缺少Authorization头
  - Authorization头格式错误
  - JWT token无效
  - JWT token已过期

- **403 Forbidden**：
  - 用户权限不足（角色检查失败）

- **500 Internal Server Error**：
  - JWT_SECRET环境变量未设置

## 日志记录

系统会记录以下认证相关日志：

```
🔓 开发模式：跳过JWT认证验证
✅ JWT验证成功，用户ID: user123
❌ JWT验证失败: invalid signature
⚠️ 可选JWT验证失败，继续执行: invalid token
```

## 安全建议

1. **生产环境**：
   - 使用强密钥作为JWT_SECRET
   - 设置合理的token过期时间
   - 确保SKIP_JWT_AUTH=false

2. **开发环境**：
   - 可以使用DEV_MODE=true简化开发流程
   - 定期测试完整的认证流程

3. **密钥管理**：
   - 不要在代码中硬编码JWT_SECRET
   - 使用环境变量或密钥管理服务
   - 定期轮换生产环境密钥

## 故障排除

### 常见问题

1. **认证总是被跳过**
   - 检查DEV_MODE和SKIP_JWT_AUTH环境变量
   - 确认NODE_ENV设置正确

2. **JWT验证失败**
   - 检查JWT_SECRET是否设置
   - 确认token格式：`Bearer <token>`
   - 检查token是否过期

3. **权限检查失败**
   - 确认用户角色信息正确
   - 检查requireRole中间件的角色配置

### 调试技巧

1. 启用详细日志记录
2. 检查请求头中的Authorization字段
3. 验证JWT token的有效性
4. 确认环境变量配置正确
