# 开发模式和缓存配置指南

## 概述

本指南介绍了AI-BI系统中新增的两个重要功能：
1. **开发模式下跳过JWT校验**：便于开发和测试
2. **Redis缓存的条件性使用**：支持优雅降级

这些功能旨在提高开发效率，同时保证生产环境的安全性和稳定性。

## 功能特性

### 🔓 JWT认证灵活配置
- ✅ 支持开发模式跳过认证
- ✅ 多种跳过认证的配置方式
- ✅ 生产环境完整安全验证
- ✅ 详细的日志记录和错误处理

### 🗄️ Redis缓存智能管理
- ✅ 条件性启用Redis缓存
- ✅ 连接失败时优雅降级
- ✅ 缓存操作失败不影响主功能
- ✅ 实时状态监控和健康检查

## 快速开始

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# JWT认证配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# 开发模式配置
DEV_MODE=true                    # 开发环境下跳过JWT验证
SKIP_JWT_AUTH=false              # 强制跳过JWT验证（慎用）

# Redis缓存配置
REDIS_URL="redis://localhost:6379"  # 设置为空字符串可禁用Redis
```

### 2. 开发环境快速配置

```bash
# 复制环境变量模板
cp backend/.env.example backend/.env

# 编辑配置文件
nano backend/.env

# 设置开发模式
DEV_MODE=true
NODE_ENV=development

# 可选：禁用Redis进行测试
# REDIS_URL=""
```

### 3. 启动应用

```bash
# 启动后端服务
cd backend
npm run dev

# 查看启动日志
# 🔓 开发模式：跳过JWT认证验证
# ✅ Redis连接成功 或 📝 Redis未配置，将使用无缓存模式
```

## 配置详解

### JWT认证配置

#### 跳过认证的条件

系统按以下优先级检查是否跳过JWT验证：

1. **SKIP_JWT_AUTH=true** → 强制跳过（任何环境）
2. **DEV_MODE=true + NODE_ENV=development** → 开发模式跳过

#### 配置示例

```bash
# 开发环境 - 跳过认证
NODE_ENV=development
DEV_MODE=true
SKIP_JWT_AUTH=false

# 测试环境 - 启用认证
NODE_ENV=test
DEV_MODE=false
SKIP_JWT_AUTH=false

# 生产环境 - 必须启用认证
NODE_ENV=production
DEV_MODE=false
SKIP_JWT_AUTH=false
```

### Redis缓存配置

#### 启用/禁用Redis

```bash
# 启用Redis
REDIS_URL="redis://localhost:6379"

# 禁用Redis（三种方式）
REDIS_URL=""                    # 方式1：空字符串
# REDIS_URL="redis://..."       # 方式2：注释掉
unset REDIS_URL                 # 方式3：删除变量
```

#### 不同环境配置

```bash
# 开发环境
REDIS_URL="redis://localhost:6379"

# 生产环境
REDIS_URL="redis://prod-server:6379"

# Docker环境
REDIS_URL="redis://redis:6379"
```

## 使用示例

### 1. 在控制器中使用认证

```typescript
import { AuthenticatedRequest } from '../middleware/auth';

export async function protectedController(req: AuthenticatedRequest, res: Response) {
  // 获取用户信息（开发模式下会有默认值）
  const userId = req.user?.id;        // 开发模式: 'dev-user-id'
  const userEmail = req.user?.email;  // 开发模式: '<EMAIL>'
  const userRole = req.user?.role;    // 开发模式: 'admin'
  
  console.log(`当前用户: ${userId}`);
  
  // 业务逻辑...
}
```

### 2. 在服务中使用缓存

```typescript
import { redisClient } from '../utils/redisClient';

export class DataService {
  async getData(id: string) {
    // 尝试从缓存获取（Redis不可用时返回null）
    const cached = await redisClient.get(`data:${id}`);
    if (cached) {
      return JSON.parse(cached);
    }
    
    // 从数据库获取
    const data = await this.fetchFromDB(id);
    
    // 缓存数据（Redis不可用时静默忽略）
    await redisClient.set(`data:${id}`, JSON.stringify(data), 3600);
    
    return data;
  }
}
```

### 3. 检查系统状态

```typescript
import { redisClient } from '../utils/redisClient';

// 检查Redis状态
const redisStatus = redisClient.getStatus();
console.log('Redis状态:', redisStatus);

// 健康检查
const isHealthy = await redisClient.healthCheck();
console.log('Redis健康:', isHealthy);
```

## 日志说明

### JWT认证日志

```bash
# 开发模式
🔓 开发模式：跳过JWT认证验证

# 生产模式
✅ JWT验证成功，用户ID: user123
❌ JWT验证失败: invalid signature
```

### Redis缓存日志

```bash
# 连接状态
✅ Redis连接成功
📝 Redis未配置，将使用无缓存模式
❌ Redis连接失败，将使用无缓存模式

# 缓存操作
✅ Redis缓存命中: user:123
❌ Redis缓存未命中: user:123
🔄 Redis不可用，跳过缓存读取: user:123
```

## 测试验证

### 1. 运行单元测试

```bash
# 测试JWT认证中间件
npm test -- --testPathPattern="auth.test.ts"

# 测试Redis客户端
npm test -- --testPathPattern="redisClient.test.ts"

# 运行所有测试
npm test
```

### 2. 手动测试

```bash
# 测试开发模式
curl -X GET http://localhost:3001/api/data-sources
# 应该成功返回数据（无需JWT token）

# 测试生产模式（设置DEV_MODE=false后）
curl -X GET http://localhost:3001/api/data-sources
# 应该返回401错误

# 测试Redis状态
curl -X GET http://localhost:3001/health
# 查看返回的Redis状态信息
```

## 故障排除

### 常见问题

1. **认证总是被跳过**
   ```bash
   # 检查环境变量
   echo $DEV_MODE $SKIP_JWT_AUTH $NODE_ENV
   
   # 确保生产环境配置正确
   DEV_MODE=false
   SKIP_JWT_AUTH=false
   NODE_ENV=production
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis服务
   redis-cli ping
   
   # 检查配置
   echo $REDIS_URL
   
   # 查看应用日志
   tail -f logs/app.log | grep Redis
   ```

3. **缓存不生效**
   ```bash
   # 检查Redis状态
   curl http://localhost:3001/health
   
   # 查看Redis监控
   redis-cli monitor
   ```

## 安全建议

### 生产环境检查清单

- [ ] `NODE_ENV=production`
- [ ] `DEV_MODE=false`
- [ ] `SKIP_JWT_AUTH=false`
- [ ] `JWT_SECRET` 使用强密钥
- [ ] Redis连接使用认证
- [ ] 定期轮换JWT密钥

### 开发环境最佳实践

- [ ] 使用 `DEV_MODE=true` 而非 `SKIP_JWT_AUTH=true`
- [ ] 定期测试完整认证流程
- [ ] 不要在代码中硬编码配置
- [ ] 使用不同的Redis数据库索引

## 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 新增开发模式JWT跳过功能
- ✨ 新增Redis条件性使用功能
- ✨ 新增详细的中文日志记录
- ✨ 新增完整的单元测试覆盖
- 📚 新增中文配置文档

## 相关文档

- [JWT认证配置说明](./JWT认证配置说明.md)
- [Redis缓存配置说明](./Redis缓存配置说明.md)
- [部署指南](./DEPLOYMENT.md)
- [API文档](./API.md)
