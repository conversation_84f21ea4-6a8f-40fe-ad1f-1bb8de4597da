import { LLMChatService } from '../services/llmChatService';
import { LLMChatRequest, LLMServiceError } from '../types';

// Mock Prisma Client
const mockPrisma = {
  lLMConversation: {
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    findMany: jest.fn()
  },
  lLMMessage: {
    create: jest.fn(),
    findMany: jest.fn()
  }
};

// Mock EnhancedLLMClient
jest.mock('../services/llm/enhancedLLMClient', () => {
  return {
    EnhancedLLMClient: jest.fn().mockImplementation(() => ({
      chat: jest.fn(),
      healthCheck: jest.fn()
    }))
  };
});

describe('LLMChatService', () => {
  let llmChatService: LLMChatService;

  beforeEach(() => {
    jest.clearAllMocks();
    llmChatService = new LLMChatService(mockPrisma as any);
  });

  describe('processChat', () => {
    const validRequest: LLMChatRequest = {
      messages: [
        { role: 'user', content: '你好，请介绍一下你自己' }
      ],
      model: 'deepseek',
      stream: false,
      conversation_id: 'conv_123456',
      options: {
        deep_thinking: true,
        online_search: false
      }
    };

    it('应该成功处理有效的聊天请求', async () => {
      // 模拟数据库响应
      mockPrisma.lLMConversation.findUnique.mockResolvedValue({
        id: 'conv_123456',
        title: null,
        userId: null,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      mockPrisma.lLMMessage.create
        .mockResolvedValueOnce({
          id: 'msg_user_123',
          conversationId: 'conv_123456',
          role: 'user',
          content: '你好，请介绍一下你自己',
          model: 'deepseek',
          createdAt: new Date()
        })
        .mockResolvedValueOnce({
          id: 'msg_assistant_123',
          conversationId: 'conv_123456',
          role: 'assistant',
          content: '你好！我是AI助手...',
          model: 'deepseek',
          createdAt: new Date()
        });

      // 模拟LLM客户端响应
      const mockLLMResponse = {
        success: true,
        data: {
          message: {
            role: 'assistant' as const,
            content: '你好！我是AI助手，很高兴为您服务。',
            id: 'msg_assistant_123',
            timestamp: Date.now()
          },
          conversation_id: 'conv_123456',
          message_id: 'msg_assistant_123',
          request_id: 'req_123456',
          model: 'deepseek' as const,
          finish_reason: 'stop' as const,
          usage: {
            prompt_tokens: 20,
            completion_tokens: 30,
            total_tokens: 50
          }
        },
        requestId: 'req_123456',
        timestamp: Date.now()
      };

      // 获取模拟的LLM客户端实例
      const mockClient = (llmChatService as any).llmClients.get('deepseek');
      mockClient.chat.mockResolvedValue(mockLLMResponse);

      const result = await llmChatService.processChat(validRequest);

      expect(result.success).toBe(true);
      expect(result.data.message.content).toContain('AI助手');
      expect(result.data.conversation_id).toBe('conv_123456');
      expect(mockPrisma.lLMMessage.create).toHaveBeenCalledTimes(2);
    });

    it('应该拒绝空消息列表', async () => {
      const invalidRequest = {
        ...validRequest,
        messages: []
      };

      await expect(llmChatService.processChat(invalidRequest))
        .rejects
        .toThrow(LLMServiceError);
    });

    it('应该拒绝不支持的模型', async () => {
      const invalidRequest = {
        ...validRequest,
        model: 'unsupported-model' as any
      };

      await expect(llmChatService.processChat(invalidRequest))
        .rejects
        .toThrow('不支持的模型');
    });

    it('应该拒绝非用户的最后消息', async () => {
      const invalidRequest = {
        ...validRequest,
        messages: [
          { role: 'user', content: '你好' },
          { role: 'assistant', content: '你好！' }
        ]
      };

      await expect(llmChatService.processChat(invalidRequest))
        .rejects
        .toThrow('最后一条消息必须是用户消息');
    });
  });

  describe('getOrCreateConversation', () => {
    it('应该返回现有对话', async () => {
      const existingConversation = {
        id: 'conv_123456',
        title: '测试对话',
        userId: 'user_123',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.lLMConversation.findUnique.mockResolvedValue(existingConversation);

      const result = await (llmChatService as any).getOrCreateConversation('conv_123456');

      expect(result.id).toBe('conv_123456');
      expect(result.title).toBe('测试对话');
      expect(mockPrisma.lLMConversation.findUnique).toHaveBeenCalledWith({
        where: { id: 'conv_123456' }
      });
    });

    it('应该创建新对话', async () => {
      mockPrisma.lLMConversation.findUnique.mockResolvedValue(null);
      
      const newConversation = {
        id: 'conv_new_123',
        title: null,
        userId: null,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.lLMConversation.create.mockResolvedValue(newConversation);

      const result = await (llmChatService as any).getOrCreateConversation('conv_new_123');

      expect(result.id).toBe('conv_new_123');
      expect(mockPrisma.lLMConversation.create).toHaveBeenCalled();
    });
  });

  describe('getConversations', () => {
    it('应该返回对话列表', async () => {
      const mockConversations = [
        {
          id: 'conv_1',
          title: '对话1',
          userId: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          messages: []
        },
        {
          id: 'conv_2',
          title: '对话2',
          userId: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          messages: []
        }
      ];

      mockPrisma.lLMConversation.findMany.mockResolvedValue(mockConversations);

      const result = await llmChatService.getConversations(undefined, 10);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('conv_1');
      expect(result[1].id).toBe('conv_2');
    });
  });

  describe('getConversationMessages', () => {
    it('应该返回对话消息列表', async () => {
      const mockMessages = [
        {
          id: 'msg_1',
          conversationId: 'conv_123',
          role: 'user',
          content: '你好',
          model: 'deepseek',
          createdAt: new Date(),
          deepThinking: false,
          onlineSearch: false
        },
        {
          id: 'msg_2',
          conversationId: 'conv_123',
          role: 'assistant',
          content: '你好！',
          model: 'deepseek',
          createdAt: new Date(),
          deepThinking: false,
          onlineSearch: false
        }
      ];

      mockPrisma.lLMMessage.findMany.mockResolvedValue(mockMessages);

      const result = await llmChatService.getConversationMessages('conv_123', 50);

      expect(result).toHaveLength(2);
      expect(result[0].role).toBe('user');
      expect(result[1].role).toBe('assistant');
    });
  });

  describe('deleteConversation', () => {
    it('应该成功删除对话', async () => {
      mockPrisma.lLMConversation.delete.mockResolvedValue({});

      await llmChatService.deleteConversation('conv_123');

      expect(mockPrisma.lLMConversation.delete).toHaveBeenCalledWith({
        where: { id: 'conv_123' }
      });
    });
  });

  describe('getSupportedModels', () => {
    it('应该返回支持的模型列表', () => {
      const models = llmChatService.getSupportedModels();

      expect(models).toContain('deepseek');
      expect(models).toContain('gpt-3.5-turbo');
      expect(models).toContain('gpt-4');
      expect(models).toContain('claude-3');
    });
  });

  describe('healthCheck', () => {
    it('应该返回所有模型的健康状态', async () => {
      // 模拟所有客户端健康检查成功
      const mockClients = (llmChatService as any).llmClients;
      for (const [model, client] of mockClients) {
        client.healthCheck.mockResolvedValue(true);
      }

      const result = await llmChatService.healthCheck();

      expect(typeof result).toBe('object');
      expect(Object.keys(result).length).toBeGreaterThan(0);
    });
  });
});
