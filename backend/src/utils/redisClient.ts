import { createClient, RedisClientType } from 'redis';

/**
 * Redis客户端包装器
 * 支持条件性使用Redis缓存，当Redis不可用时优雅降级
 */
export class RedisClientWrapper {
  private client: RedisClientType | null = null;
  private isConnected: boolean = false;
  private isEnabled: boolean = false;
  private connectionAttempted: boolean = false;

  constructor() {
    this.isEnabled = this.shouldEnableRedis();
  }

  /**
   * 检查是否应该启用Redis
   * 基于REDIS_URL环境变量是否设置且不为空
   */
  private shouldEnableRedis(): boolean {
    const redisUrl = process.env.REDIS_URL;
    
    if (!redisUrl || redisUrl.trim() === '') {
      console.log('📝 Redis未配置：REDIS_URL环境变量为空，将跳过Redis缓存功能');
      return false;
    }
    
    console.log('📝 Redis已配置：将尝试连接Redis服务器');
    return true;
  }

  /**
   * 初始化Redis连接
   * 如果连接失败，会优雅降级而不是抛出错误
   */
  async connect(): Promise<void> {
    if (!this.isEnabled) {
      console.log('⚠️ Redis已禁用，跳过连接');
      return;
    }

    if (this.connectionAttempted) {
      return;
    }

    this.connectionAttempted = true;

    try {
      const redisUrl = process.env.REDIS_URL!;
      this.client = createClient({ url: redisUrl });

      // 监听连接事件
      this.client.on('connect', () => {
        console.log('🔄 正在连接Redis...');
      });

      this.client.on('ready', () => {
        console.log('✅ Redis连接成功');
        this.isConnected = true;
      });

      this.client.on('error', (error) => {
        console.error('❌ Redis连接错误:', error.message);
        this.isConnected = false;
      });

      this.client.on('end', () => {
        console.log('🔌 Redis连接已断开');
        this.isConnected = false;
      });

      // 尝试连接
      await this.client.connect();
      
    } catch (error) {
      console.error('❌ Redis连接失败，将使用无缓存模式:', (error as Error).message);
      this.isConnected = false;
      this.client = null;
    }
  }

  /**
   * 断开Redis连接
   */
  async disconnect(): Promise<void> {
    if (this.client && this.isConnected) {
      try {
        await this.client.quit();
        console.log('✅ Redis连接已关闭');
      } catch (error) {
        console.error('❌ 关闭Redis连接时出错:', (error as Error).message);
      }
    }
    this.isConnected = false;
    this.client = null;
  }

  /**
   * 检查Redis是否可用
   */
  isAvailable(): boolean {
    return this.isEnabled && this.isConnected && this.client !== null;
  }

  /**
   * 获取缓存值
   * 如果Redis不可用，返回null
   */
  async get(key: string): Promise<string | null> {
    if (!this.isAvailable()) {
      console.log(`🔄 Redis不可用，跳过缓存读取: ${key}`);
      return null;
    }

    try {
      const value = await this.client!.get(key);
      if (value) {
        console.log(`✅ Redis缓存命中: ${key}`);
      } else {
        console.log(`❌ Redis缓存未命中: ${key}`);
      }
      return value;
    } catch (error) {
      console.error(`❌ Redis读取失败，key: ${key}, 错误:`, (error as Error).message);
      return null;
    }
  }

  /**
   * 设置缓存值
   * 如果Redis不可用，静默忽略
   */
  async set(key: string, value: string, ttlSeconds?: number): Promise<boolean> {
    if (!this.isAvailable()) {
      console.log(`🔄 Redis不可用，跳过缓存写入: ${key}`);
      return false;
    }

    try {
      if (ttlSeconds) {
        await this.client!.setEx(key, ttlSeconds, value);
      } else {
        await this.client!.set(key, value);
      }
      console.log(`✅ Redis缓存写入成功: ${key}`);
      return true;
    } catch (error) {
      console.error(`❌ Redis写入失败，key: ${key}, 错误:`, (error as Error).message);
      return false;
    }
  }

  /**
   * 删除缓存值
   * 如果Redis不可用，静默忽略
   */
  async del(key: string): Promise<boolean> {
    if (!this.isAvailable()) {
      console.log(`🔄 Redis不可用，跳过缓存删除: ${key}`);
      return false;
    }

    try {
      const result = await this.client!.del(key);
      console.log(`✅ Redis缓存删除: ${key}, 结果: ${result}`);
      return result > 0;
    } catch (error) {
      console.error(`❌ Redis删除失败，key: ${key}, 错误:`, (error as Error).message);
      return false;
    }
  }

  /**
   * 检查键是否存在
   * 如果Redis不可用，返回false
   */
  async exists(key: string): Promise<boolean> {
    if (!this.isAvailable()) {
      console.log(`🔄 Redis不可用，跳过存在性检查: ${key}`);
      return false;
    }

    try {
      const result = await this.client!.exists(key);
      return result > 0;
    } catch (error) {
      console.error(`❌ Redis存在性检查失败，key: ${key}, 错误:`, (error as Error).message);
      return false;
    }
  }

  /**
   * 设置键的过期时间
   * 如果Redis不可用，静默忽略
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    if (!this.isAvailable()) {
      console.log(`🔄 Redis不可用，跳过过期时间设置: ${key}`);
      return false;
    }

    try {
      const result = await this.client!.expire(key, seconds);
      return result;
    } catch (error) {
      console.error(`❌ Redis设置过期时间失败，key: ${key}, 错误:`, (error as Error).message);
      return false;
    }
  }

  /**
   * 获取Redis连接状态信息
   */
  getStatus(): {
    enabled: boolean;
    connected: boolean;
    available: boolean;
    redisUrl?: string;
  } {
    return {
      enabled: this.isEnabled,
      connected: this.isConnected,
      available: this.isAvailable(),
      redisUrl: this.isEnabled ? process.env.REDIS_URL : undefined
    };
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await this.client!.ping();
      return true;
    } catch (error) {
      console.error('❌ Redis健康检查失败:', (error as Error).message);
      return false;
    }
  }
}

// 创建全局Redis客户端实例
export const redisClient = new RedisClientWrapper();
