import crypto from 'crypto';

const ALGORITHM = 'aes-256-cbc';
const SECRET_KEY = process.env.ENCRYPTION_KEY || 'your-32-character-secret-key-here!!';

// 确保密钥长度为32字节
const key = crypto.scryptSync(SECRET_KEY, 'salt', 32);

/**
 * 加密敏感数据
 */
export function encrypt(text: string): string {
  try {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // 返回格式: iv:encryptedData
    return `${iv.toString('hex')}:${encrypted}`;
  } catch (error) {
    console.error('加密错误详情:', error);
    throw new Error('加密失败');
  }
}

/**
 * 解密敏感数据
 */
export function decrypt(encryptedData: string): string {
  try {
    const parts = encryptedData.split(':');
    if (parts.length !== 2) {
      throw new Error('无效的加密数据格式');
    }

    const [ivHex, encrypted] = parts;
    const iv = Buffer.from(ivHex, 'hex');

    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    throw new Error('解密失败');
  }
}

/**
 * 加密连接配置
 */
export function encryptConnectionConfig(config: any): string {
  return encrypt(JSON.stringify(config));
}

/**
 * 解密连接配置
 */
export function decryptConnectionConfig(encryptedConfig: string): any {
  const decryptedString = decrypt(encryptedConfig);
  return JSON.parse(decryptedString);
}
