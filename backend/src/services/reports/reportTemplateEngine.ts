import { PrismaClient } from '@prisma/client';
import {
  ReportTemplate,
  ReportSection,
  UserRole,
  DatabaseError,
  ValidationError
} from '../../types';

/**
 * 报表模板引擎
 * 负责报表模板的创建、管理和配置
 */
export class ReportTemplateEngine {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * 创建报表模板
   */
  async createTemplate(templateData: Omit<ReportTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<ReportTemplate> {
    try {
      console.log(`🎨 创建报表模板: ${templateData.name}`);

      // 验证模板配置
      this.validateTemplateConfig(templateData.config);

      const template = await this.prisma.reportTemplate.create({
        data: {
          name: templateData.name,
          description: templateData.description,
          config: JSON.stringify(templateData.config),
          userRole: templateData.userRole,
          domain: templateData.domain,
          isPublic: templateData.isPublic,
          createdBy: templateData.createdBy
        }
      });

      console.log(`✅ 报表模板创建成功: ${template.id}`);

      return {
        id: template.id,
        name: template.name,
        description: template.description || undefined,
        config: JSON.parse(template.config),
        userRole: template.userRole as UserRole,
        domain: template.domain,
        isPublic: template.isPublic,
        createdBy: template.createdBy,
        createdAt: template.createdAt.toISOString(),
        updatedAt: template.updatedAt.toISOString()
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 创建报表模板失败:', errorMessage);
      throw new DatabaseError(`创建报表模板失败: ${errorMessage}`);
    }
  }

  /**
   * 获取报表模板
   */
  async getTemplate(templateId: string): Promise<ReportTemplate | null> {
    try {
      const template = await this.prisma.reportTemplate.findUnique({
        where: { id: templateId }
      });

      if (!template) {
        return null;
      }

      return {
        id: template.id,
        name: template.name,
        description: template.description || undefined,
        config: JSON.parse(template.config),
        userRole: template.userRole as UserRole,
        domain: template.domain,
        isPublic: template.isPublic,
        createdBy: template.createdBy,
        createdAt: template.createdAt.toISOString(),
        updatedAt: template.updatedAt.toISOString()
      };
    } catch (error) {
      throw new DatabaseError('获取报表模板失败');
    }
  }

  /**
   * 获取用户可用的模板列表
   */
  async getAvailableTemplates(userRole: UserRole, userId?: string): Promise<ReportTemplate[]> {
    try {
      console.log(`📋 获取${userRole}角色的可用模板`);

      const templates = await this.prisma.reportTemplate.findMany({
        where: {
          OR: [
            { userRole }, // 角色匹配的模板
            { isPublic: true }, // 公共模板
            { createdBy: userId || '' } // 用户自己创建的模板
          ]
        },
        orderBy: [
          { isPublic: 'desc' }, // 公共模板优先
          { createdAt: 'desc' }
        ]
      });

      return templates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description || undefined,
        config: JSON.parse(template.config),
        userRole: template.userRole as UserRole,
        domain: template.domain,
        isPublic: template.isPublic,
        createdBy: template.createdBy,
        createdAt: template.createdAt.toISOString(),
        updatedAt: template.updatedAt.toISOString()
      }));
    } catch (error) {
      throw new DatabaseError('获取可用模板列表失败');
    }
  }

  /**
   * 更新报表模板
   */
  async updateTemplate(
    templateId: string,
    updateData: Partial<Omit<ReportTemplate, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<ReportTemplate> {
    try {
      console.log(`🔄 更新报表模板: ${templateId}`);

      // 验证更新的配置
      if (updateData.config) {
        this.validateTemplateConfig(updateData.config);
      }

      const updatePayload: any = {};
      if (updateData.name) updatePayload.name = updateData.name;
      if (updateData.description !== undefined) updatePayload.description = updateData.description;
      if (updateData.config) updatePayload.config = JSON.stringify(updateData.config);
      if (updateData.userRole) updatePayload.userRole = updateData.userRole;
      if (updateData.domain) updatePayload.domain = updateData.domain;
      if (updateData.isPublic !== undefined) updatePayload.isPublic = updateData.isPublic;

      const template = await this.prisma.reportTemplate.update({
        where: { id: templateId },
        data: updatePayload
      });

      console.log(`✅ 报表模板更新成功: ${templateId}`);

      return {
        id: template.id,
        name: template.name,
        description: template.description || undefined,
        config: JSON.parse(template.config),
        userRole: template.userRole as UserRole,
        domain: template.domain,
        isPublic: template.isPublic,
        createdBy: template.createdBy,
        createdAt: template.createdAt.toISOString(),
        updatedAt: template.updatedAt.toISOString()
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 更新报表模板失败:', errorMessage);
      throw new DatabaseError(`更新报表模板失败: ${errorMessage}`);
    }
  }

  /**
   * 删除报表模板
   */
  async deleteTemplate(templateId: string): Promise<void> {
    try {
      console.log(`🗑️ 删除报表模板: ${templateId}`);

      // 检查是否有关联的报表实例
      const instanceCount = await this.prisma.reportInstance.count({
        where: { templateId }
      });

      if (instanceCount > 0) {
        throw new ValidationError(`无法删除模板，存在${instanceCount}个关联的报表实例`);
      }

      await this.prisma.reportTemplate.delete({
        where: { id: templateId }
      });

      console.log(`✅ 报表模板删除成功: ${templateId}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 删除报表模板失败:', errorMessage);
      
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError(`删除报表模板失败: ${errorMessage}`);
    }
  }

  /**
   * 复制报表模板
   */
  async cloneTemplate(templateId: string, newName: string, createdBy: string): Promise<ReportTemplate> {
    try {
      console.log(`📋 复制报表模板: ${templateId} -> ${newName}`);

      const originalTemplate = await this.getTemplate(templateId);
      if (!originalTemplate) {
        throw new ValidationError('原始模板不存在');
      }

      const clonedTemplate = await this.createTemplate({
        name: newName,
        description: `基于"${originalTemplate.name}"复制的模板`,
        config: originalTemplate.config,
        userRole: originalTemplate.userRole,
        domain: originalTemplate.domain,
        isPublic: false, // 复制的模板默认为私有
        createdBy
      });

      console.log(`✅ 报表模板复制成功: ${clonedTemplate.id}`);
      return clonedTemplate;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 复制报表模板失败:', errorMessage);
      throw new DatabaseError(`复制报表模板失败: ${errorMessage}`);
    }
  }

  /**
   * 验证模板配置
   */
  private validateTemplateConfig(config: any): void {
    if (!config) {
      throw new ValidationError('模板配置不能为空');
    }

    if (!config.sections || !Array.isArray(config.sections)) {
      throw new ValidationError('模板必须包含sections数组');
    }

    if (config.sections.length === 0) {
      throw new ValidationError('模板至少需要包含一个章节');
    }

    // 验证每个章节的配置
    for (const section of config.sections) {
      this.validateSectionConfig(section);
    }

    if (!config.dataSourceIds || !Array.isArray(config.dataSourceIds)) {
      throw new ValidationError('模板必须指定数据源ID列表');
    }

    if (!config.exportFormats || !Array.isArray(config.exportFormats)) {
      throw new ValidationError('模板必须指定导出格式列表');
    }
  }

  /**
   * 验证章节配置
   */
  private validateSectionConfig(section: ReportSection): void {
    if (!section.id) {
      throw new ValidationError('章节必须有唯一ID');
    }

    if (!section.title) {
      throw new ValidationError('章节必须有标题');
    }

    if (!section.type) {
      throw new ValidationError('章节必须指定类型');
    }

    const validTypes = ['summary', 'chart', 'table', 'insight', 'text'];
    if (!validTypes.includes(section.type)) {
      throw new ValidationError(`无效的章节类型: ${section.type}`);
    }

    if (typeof section.order !== 'number') {
      throw new ValidationError('章节必须指定排序号');
    }

    if (!section.config) {
      throw new ValidationError('章节必须有配置信息');
    }
  }

  /**
   * 获取预定义模板
   */
  async getPreDefinedTemplates(): Promise<ReportTemplate[]> {
    try {
      const templates = await this.prisma.reportTemplate.findMany({
        where: {
          createdBy: 'system',
          isPublic: true
        },
        orderBy: { createdAt: 'asc' }
      });

      return templates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description || undefined,
        config: JSON.parse(template.config),
        userRole: template.userRole as UserRole,
        domain: template.domain,
        isPublic: template.isPublic,
        createdBy: template.createdBy,
        createdAt: template.createdAt.toISOString(),
        updatedAt: template.updatedAt.toISOString()
      }));
    } catch (error) {
      throw new DatabaseError('获取预定义模板失败');
    }
  }

  /**
   * 初始化系统默认模板
   */
  async initializeDefaultTemplates(): Promise<void> {
    try {
      console.log('🔧 初始化系统默认模板');

      const existingTemplates = await this.prisma.reportTemplate.count({
        where: { createdBy: 'system' }
      });

      if (existingTemplates > 0) {
        console.log('📋 系统默认模板已存在，跳过初始化');
        return;
      }

      // 创建高管模板
      await this.createTemplate({
        name: '高管仪表板模板',
        description: '为高管角色设计的战略级报表模板',
        config: this.getExecutiveTemplateConfig(),
        userRole: 'executive',
        domain: 'strategic',
        isPublic: true,
        createdBy: 'system'
      });

      // 创建经理模板
      await this.createTemplate({
        name: '管理层运营报表模板',
        description: '为管理层设计的运营级报表模板',
        config: this.getManagerTemplateConfig(),
        userRole: 'manager',
        domain: 'operational',
        isPublic: true,
        createdBy: 'system'
      });

      // 创建分析师模板
      await this.createTemplate({
        name: '分析师详细报表模板',
        description: '为分析师设计的详细分析报表模板',
        config: this.getAnalystTemplateConfig(),
        userRole: 'analyst',
        domain: 'analytical',
        isPublic: true,
        createdBy: 'system'
      });

      console.log('✅ 系统默认模板初始化完成');
    } catch (error) {
      console.error('❌ 初始化系统默认模板失败:', error);
      throw new DatabaseError('初始化系统默认模板失败');
    }
  }

  /**
   * 获取高管模板配置
   */
  private getExecutiveTemplateConfig() {
    return {
      sections: [
        {
          id: 'executive-summary',
          title: '执行摘要',
          type: 'summary' as const,
          config: { includeKPIs: true, includeAlerts: true },
          order: 1
        },
        {
          id: 'key-metrics',
          title: '关键指标',
          type: 'chart' as const,
          config: { chartType: 'dashboard', showTrends: true },
          order: 2
        },
        {
          id: 'strategic-insights',
          title: '战略洞察',
          type: 'insight' as const,
          config: { focusLevel: 'strategic' },
          order: 3
        }
      ],
      dataSourceIds: [],
      refreshInterval: 3600,
      exportFormats: ['pdf', 'html']
    };
  }

  /**
   * 获取经理模板配置
   */
  private getManagerTemplateConfig() {
    return {
      sections: [
        {
          id: 'performance-overview',
          title: '绩效概览',
          type: 'summary' as const,
          config: { includeComparisons: true },
          order: 1
        },
        {
          id: 'team-metrics',
          title: '团队指标',
          type: 'chart' as const,
          config: { chartType: 'multi-series', groupBy: 'team' },
          order: 2
        },
        {
          id: 'operational-insights',
          title: '运营洞察',
          type: 'insight' as const,
          config: { focusLevel: 'operational' },
          order: 3
        },
        {
          id: 'action-items',
          title: '行动项目',
          type: 'table' as const,
          config: { showPriority: true, showOwner: true },
          order: 4
        }
      ],
      dataSourceIds: [],
      refreshInterval: 1800,
      exportFormats: ['pdf', 'excel', 'html']
    };
  }

  /**
   * 获取分析师模板配置
   */
  private getAnalystTemplateConfig() {
    return {
      sections: [
        {
          id: 'data-summary',
          title: '数据摘要',
          type: 'summary' as const,
          config: { includeDataQuality: true },
          order: 1
        },
        {
          id: 'detailed-analysis',
          title: '详细分析',
          type: 'chart' as const,
          config: { chartType: 'detailed', showStatistics: true },
          order: 2
        },
        {
          id: 'data-insights',
          title: '数据洞察',
          type: 'insight' as const,
          config: { focusLevel: 'analytical', includeMethodology: true },
          order: 3
        },
        {
          id: 'raw-data',
          title: '原始数据',
          type: 'table' as const,
          config: { showFilters: true, allowExport: true },
          order: 4
        }
      ],
      dataSourceIds: [],
      refreshInterval: 900,
      exportFormats: ['excel', 'csv', 'html']
    };
  }
}
