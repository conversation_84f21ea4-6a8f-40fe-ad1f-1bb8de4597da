import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Download as DownloadIcon,
  Share as ShareIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendIcon,
  Warning as WarningIcon,
  Lightbulb as InsightIcon,
  Assessment as ChartIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { reportApi } from '../services/api';
import { ReportInstance, DataInsight } from '../types';

/**
 * 报表详情页面
 * 显示完整的报表内容，包括执行摘要、章节内容、洞察和行动项目
 */
const ReportDetail: React.FC = () => {
  const { reportId } = useParams<{ reportId: string }>();
  const navigate = useNavigate();
  const [expandedSection, setExpandedSection] = useState<string | false>('summary');

  // 获取报表详情
  const { data: report, isLoading, error, refetch } = useQuery(
    ['report', reportId],
    () => reportApi.getReport(reportId!),
    {
      enabled: !!reportId,
      refetchInterval: (data) => {
        // 如果报表还在生成中，每5秒刷新一次
        return data?.status === 'generating' ? 5000 : false;
      }
    }
  );

  // 处理章节展开/收起
  const handleSectionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedSection(isExpanded ? panel : false);
  };

  // 获取洞察图标
  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'trend':
        return <TrendIcon color="primary" />;
      case 'anomaly':
        return <WarningIcon color="warning" />;
      case 'opportunity':
        return <InsightIcon color="success" />;
      case 'risk':
        return <WarningIcon color="error" />;
      default:
        return <InsightIcon />;
    }
  };

  // 获取洞察严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  // 渲染图表内容
  const renderChartContent = (content: any) => {
    if (content.chart) {
      return (
        <Box sx={{ mt: 2 }}>
          <Typography variant="h6" gutterBottom>
            {content.chart.spec.title || '数据图表'}
          </Typography>
          {/* 这里应该集成实际的图表库，如 Recharts 或 Chart.js */}
          <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
            <Typography variant="body2" color="textSecondary">
              图表类型: {content.chart.type}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              数据点: {content.data?.length || 0} 个
            </Typography>
            {/* 简单的数据表格展示 */}
            {content.data && content.data.length > 0 && (
              <TableContainer sx={{ mt: 2 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      {Object.keys(content.data[0]).map((key) => (
                        <TableCell key={key}>{key}</TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {content.data.slice(0, 5).map((row: any, index: number) => (
                      <TableRow key={index}>
                        {Object.values(row).map((value: any, cellIndex) => (
                          <TableCell key={cellIndex}>{String(value)}</TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Paper>
        </Box>
      );
    }
    return null;
  };

  // 渲染表格内容
  const renderTableContent = (content: any) => {
    if (content.data && content.columns) {
      return (
        <TableContainer component={Paper} sx={{ mt: 2 }}>
          <Table>
            <TableHead>
              <TableRow>
                {content.columns.map((column: any) => (
                  <TableCell key={column.key}>{column.title}</TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {content.data.map((row: any, index: number) => (
                <TableRow key={index}>
                  {content.columns.map((column: any) => (
                    <TableCell key={column.key}>
                      {row[column.key]}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>加载报表中...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          加载报表失败: {(error as Error).message}
        </Alert>
        <Button onClick={() => navigate('/reports')} sx={{ mt: 2 }}>
          返回报表列表
        </Button>
      </Box>
    );
  }

  if (!report) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          报表不存在
        </Alert>
        <Button onClick={() => navigate('/reports')} sx={{ mt: 2 }}>
          返回报表列表
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* 页面头部 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={() => navigate('/reports')} sx={{ mr: 1 }}>
            <BackIcon />
          </IconButton>
          <Typography variant="h4" component="h1">
            {report.title}
          </Typography>
          <Chip
            label={report.status === 'completed' ? '已完成' : report.status === 'generating' ? '生成中' : '失败'}
            color={report.status === 'completed' ? 'success' : report.status === 'generating' ? 'warning' : 'error'}
            sx={{ ml: 2 }}
          />
        </Box>
        <Box>
          <Tooltip title="刷新">
            <IconButton onClick={() => refetch()}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="分享">
            <IconButton>
              <ShareIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="下载">
            <IconButton disabled={report.status !== 'completed'}>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* 报表元信息 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={3}>
              <Typography variant="body2" color="textSecondary">生成时间</Typography>
              <Typography variant="body1">
                {new Date(report.generatedAt).toLocaleString('zh-CN')}
              </Typography>
            </Grid>
            <Grid item xs={12} md={3}>
              <Typography variant="body2" color="textSecondary">置信度</Typography>
              <Typography variant="body1">
                {(report.content.metadata.confidence * 100).toFixed(1)}%
              </Typography>
            </Grid>
            <Grid item xs={12} md={3}>
              <Typography variant="body2" color="textSecondary">章节数量</Typography>
              <Typography variant="body1">
                {report.content.sections.length} 个
              </Typography>
            </Grid>
            <Grid item xs={12} md={3}>
              <Typography variant="body2" color="textSecondary">洞察数量</Typography>
              <Typography variant="body1">
                {report.content.keyInsights.length} 个
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 报表内容 */}
      {report.status === 'generating' ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <CircularProgress sx={{ mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              报表生成中...
            </Typography>
            <Typography variant="body2" color="textSecondary">
              预计还需要几分钟时间，请稍候
            </Typography>
          </CardContent>
        </Card>
      ) : report.status === 'completed' ? (
        <Box>
          {/* 执行摘要 */}
          <Accordion
            expanded={expandedSection === 'summary'}
            onChange={handleSectionChange('summary')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">📋 执行摘要</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body1" sx={{ lineHeight: 1.8 }}>
                {report.content.executiveSummary}
              </Typography>
            </AccordionDetails>
          </Accordion>

          {/* 报表章节 */}
          {report.content.sections.map((section, index) => (
            <Accordion
              key={section.id}
              expanded={expandedSection === section.id}
              onChange={handleSectionChange(section.id)}
            >
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {section.type === 'chart' && <ChartIcon sx={{ mr: 1 }} />}
                  <Typography variant="h6">
                    {section.title}
                  </Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                {section.content.text && (
                  <Typography variant="body1" sx={{ mb: 2, lineHeight: 1.8 }}>
                    {section.content.text}
                  </Typography>
                )}
                {section.type === 'chart' && renderChartContent(section.content)}
                {section.type === 'table' && renderTableContent(section.content)}
                {section.content.insights && section.content.insights.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      关键洞察:
                    </Typography>
                    <List dense>
                      {section.content.insights.map((insight, idx) => (
                        <ListItem key={idx}>
                          <ListItemIcon>
                            <InsightIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText primary={insight} />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}
              </AccordionDetails>
            </Accordion>
          ))}

          {/* 关键洞察 */}
          <Accordion
            expanded={expandedSection === 'insights'}
            onChange={handleSectionChange('insights')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">💡 关键洞察</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                {report.content.keyInsights.map((insight) => (
                  <Grid item xs={12} md={6} key={insight.id}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                          {getInsightIcon(insight.type)}
                          <Box sx={{ ml: 1, flexGrow: 1 }}>
                            <Typography variant="subtitle1" gutterBottom>
                              {insight.title}
                            </Typography>
                            <Chip
                              label={insight.severity}
                              color={getSeverityColor(insight.severity) as any}
                              size="small"
                              sx={{ mb: 1 }}
                            />
                          </Box>
                        </Box>
                        <Typography variant="body2" color="textSecondary">
                          {insight.description}
                        </Typography>
                        <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
                          置信度: {(insight.confidence * 100).toFixed(0)}%
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* 行动项目 */}
          <Accordion
            expanded={expandedSection === 'actions'}
            onChange={handleSectionChange('actions')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">🎯 行动项目</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List>
                {report.content.actionItems.map((action, index) => (
                  <ListItem key={index}>
                    <ListItemText
                      primary={action}
                      secondary={`优先级: ${index < 3 ? '高' : index < 6 ? '中' : '低'}`}
                    />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        </Box>
      ) : (
        <Alert severity="error">
          报表生成失败，请重新生成
        </Alert>
      )}
    </Box>
  );
};

export default ReportDetail;
